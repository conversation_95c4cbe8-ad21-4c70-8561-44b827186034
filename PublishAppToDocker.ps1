# Maxwell Customizations - Docker Publishing Script
# This script handles dependency management and Docker container setup for Business Central

param(
    [Parameter(Mandatory=$false)]
    [string]$ContainerName = "maxwell-bc-container",
    
    [Parameter(Mandatory=$false)]
    [string]$ImageName = "mcr.microsoft.com/businesscentral/onprem:26.0",
    
    [Parameter(Mandatory=$false)]
    [switch]$Force,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipDependencies,
    
    [Parameter(Mandatory=$false)]
    [string]$LicenseFile = ""
)

# Import required modules
Import-Module BcContainerHelper -Force

# Global configuration
$Global:SettingsFile = ".\settings.json"
$Global:AppJsonFile = ".\app.json"
$Global:DependenciesPath = ".\.dependencies"
$Global:OutputPath = ".\output"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $(
        switch($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "White" }
        }
    )
}

function Read-SettingsJson {
    if (-not (Test-Path $Global:SettingsFile)) {
        throw "settings.json file not found. Please ensure it exists in the project root."
    }
    
    try {
        $settings = Get-Content $Global:SettingsFile -Raw | ConvertFrom-Json
        return $settings
    }
    catch {
        throw "Failed to parse settings.json: $($_.Exception.Message)"
    }
}

function Read-AppJson {
    if (-not (Test-Path $Global:AppJsonFile)) {
        throw "app.json file not found. Please ensure it exists in the project root."
    }
    
    try {
        $appInfo = Get-Content $Global:AppJsonFile -Raw | ConvertFrom-Json
        return $appInfo
    }
    catch {
        throw "Failed to parse app.json: $($_.Exception.Message)"
    }
}

function Initialize-DependenciesFolder {
    if (-not (Test-Path $Global:DependenciesPath)) {
        Write-Log "Creating dependencies folder: $Global:DependenciesPath"
        New-Item -ItemType Directory -Path $Global:DependenciesPath -Force | Out-Null
    }
}

function Download-AppDependencies {
    param([object]$Settings)
    
    if ($SkipDependencies) {
        Write-Log "Skipping dependency download as requested" -Level "WARN"
        return
    }
    
    Initialize-DependenciesFolder
    
    if (-not $Settings.dependencies -or -not $Settings.dependencies.appDependencies) {
        Write-Log "No app dependencies defined in settings.json" -Level "WARN"
        return
    }
    
    foreach ($dependency in $Settings.dependencies.appDependencies) {
        Write-Log "Processing dependency: $($dependency.name)"
        
        $localPath = $dependency.localPath
        if (-not $localPath) {
            $localPath = Join-Path $Global:DependenciesPath $dependency.name.Replace(" ", "")
        }
        
        # Create local dependency folder
        if (-not (Test-Path $localPath)) {
            New-Item -ItemType Directory -Path $localPath -Force | Out-Null
        }
        
        # Check if GitHub repo is available
        if ($dependency.githubRepo) {
            Write-Log "Downloading from GitHub: $($dependency.githubRepo)"
            try {
                # Clone or pull the repository
                if (Test-Path (Join-Path $localPath ".git")) {
                    Write-Log "Updating existing repository in $localPath"
                    Push-Location $localPath
                    git pull origin main 2>$null
                    if ($LASTEXITCODE -ne 0) {
                        git pull origin master 2>$null
                    }
                    Pop-Location
                } else {
                    Write-Log "Cloning repository to $localPath"
                    git clone $dependency.githubRepo $localPath 2>$null
                    if ($LASTEXITCODE -ne 0) {
                        Write-Log "Failed to clone repository: $($dependency.githubRepo)" -Level "ERROR"
                        continue
                    }
                }
                Write-Log "Successfully downloaded: $($dependency.name)" -Level "SUCCESS"
            }
            catch {
                Write-Log "Failed to download dependency $($dependency.name): $($_.Exception.Message)" -Level "ERROR"
            }
        }
        elseif ($dependency.downloadUrl) {
            Write-Log "Downloading from URL: $($dependency.downloadUrl)"
            try {
                $fileName = "$($dependency.name.Replace(' ', '_'))_$($dependency.version).app"
                $filePath = Join-Path $localPath $fileName
                Invoke-WebRequest -Uri $dependency.downloadUrl -OutFile $filePath
                Write-Log "Successfully downloaded: $fileName" -Level "SUCCESS"
            }
            catch {
                Write-Log "Failed to download dependency from URL: $($_.Exception.Message)" -Level "ERROR"
            }
        }
        else {
            Write-Log "No download source specified for dependency: $($dependency.name)" -Level "WARN"
        }
    }
}

function Remove-ExistingContainer {
    param([string]$Name)
    
    $container = Get-BcContainers | Where-Object { $_.ContainerName -eq $Name }
    if ($container) {
        Write-Log "Removing existing container: $Name"
        Remove-BcContainer -containerName $Name -force
    }
}

function PublishAppToDocker {
    param(
        [object]$Settings,
        [object]$AppInfo
    )
    
    try {
        Write-Log "Starting Docker container creation and app publishing process"
        
        # Remove existing container if Force is specified
        if ($Force) {
            Remove-ExistingContainer -Name $ContainerName
        }
        
        # Prepare container parameters
        $containerParams = @{
            accept_eula = $true
            containerName = $ContainerName
            imageName = $ImageName
            auth = $Settings.docker.auth
            username = $Settings.docker.username
            password = $Settings.docker.password
            updateHosts = $true
            useSSL = $Settings.docker.useSSL
            memoryLimit = $Settings.docker.memory
            isolation = $Settings.docker.isolation
            includeTestToolkit = $Settings.docker.includeTestToolkit
            enableSymbolLoading = $Settings.docker.enableSymbolLoading
            shortcuts = $Settings.docker.shortcuts
        }
        
        # Add license file if specified
        if ($LicenseFile -and (Test-Path $LicenseFile)) {
            $containerParams.licenseFile = $LicenseFile
        }
        elseif ($Settings.docker.licenseFile -and (Test-Path $Settings.docker.licenseFile)) {
            $containerParams.licenseFile = $Settings.docker.licenseFile
        }
        
        Write-Log "Creating Business Central container: $ContainerName"
        New-BcContainer @containerParams
        
        # Wait for container to be ready
        Write-Log "Waiting for container to be ready..."
        Wait-BcContainerReady -containerName $ContainerName -timeout 300
        
        # Install dependencies first
        if ($Settings.dependencies -and $Settings.dependencies.appDependencies) {
            foreach ($dependency in $Settings.dependencies.appDependencies) {
                $localPath = $dependency.localPath
                if (-not $localPath) {
                    $localPath = Join-Path $Global:DependenciesPath $dependency.name.Replace(" ", "")
                }
                
                # Look for .app files in the dependency folder
                $appFiles = Get-ChildItem -Path $localPath -Filter "*.app" -Recurse
                foreach ($appFile in $appFiles) {
                    try {
                        Write-Log "Installing dependency app: $($appFile.Name)"
                        Publish-BcContainerApp -containerName $ContainerName -appFile $appFile.FullName -sync -install
                        Write-Log "Successfully installed: $($appFile.Name)" -Level "SUCCESS"
                    }
                    catch {
                        Write-Log "Failed to install dependency $($appFile.Name): $($_.Exception.Message)" -Level "ERROR"
                    }
                }
            }
        }
        
        # Build and publish the main app
        Write-Log "Building Maxwell Customizations app"
        
        # Ensure output directory exists
        if (-not (Test-Path $Global:OutputPath)) {
            New-Item -ItemType Directory -Path $Global:OutputPath -Force | Out-Null
        }
        
        $appFileName = "$($AppInfo.name.Replace(' ', '_'))_$($AppInfo.version).app"
        $appFilePath = Join-Path $Global:OutputPath $appFileName
        
        # Compile the app
        Compile-AppInBcContainer -containerName $ContainerName -appProjectFolder "." -appOutputFile $appFilePath
        
        # Publish and install the app
        Write-Log "Publishing and installing Maxwell Customizations app"
        Publish-BcContainerApp -containerName $ContainerName -appFile $appFilePath -sync -install
        
        Write-Log "Successfully published Maxwell Customizations to Docker container!" -Level "SUCCESS"
        Write-Log "Container Name: $ContainerName"
        Write-Log "Web Client URL: http://$(Get-BcContainerIpAddress -containerName $ContainerName):8080/BC/"
        Write-Log "Username: $($Settings.docker.username)"
        Write-Log "Password: $($Settings.docker.password)"
        
    }
    catch {
        Write-Log "Failed to publish app to Docker: $($_.Exception.Message)" -Level "ERROR"
        throw
    }
}

function Main {
    try {
        Write-Log "Maxwell Customizations - Docker Publishing Script Started"
        Write-Log "Container Name: $ContainerName"
        Write-Log "Image Name: $ImageName"
        
        # Read configuration files
        $settings = Read-SettingsJson
        $appInfo = Read-AppJson
        
        Write-Log "Loaded app info: $($appInfo.name) v$($appInfo.version)"
        Write-Log "Publisher: $($appInfo.publisher)"
        
        # Download dependencies
        Download-AppDependencies -Settings $settings
        
        # Publish app to Docker
        PublishAppToDocker -Settings $settings -AppInfo $appInfo
        
        Write-Log "Docker publishing process completed successfully!" -Level "SUCCESS"
    }
    catch {
        Write-Log "Script execution failed: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
}

# Execute main function
Main