codeunit 61001 "Maxwell Basic Functions Test"
{
    Subtype = Test;

    var
        SimpleAssert: Codeunit "Simple Assert MXW";
        MaxwellBasicFunctions: Codeunit "Maxwell Basic Functions MXW";

    [Test]
    procedure TestGetItemDescription_WithValidItemAndVariant()
    var
        Item: Record Item;
        ItemVariant: Record "Item Variant";
        Result: Text[100];
        ExpectedDescription: Text[100];
    begin
        // [GIVEN] An item and variant exist with descriptions
        CreateTestItem(Item);
        CreateTestItemVariant(ItemVariant, Item."No.");
        
        ExpectedDescription := ItemVariant.Description;

        // [WHEN] Getting item description with variant
        Result := MaxwellBasicFunctions.GetItemDescription(Item."No.", ItemVariant.Code);

        // [THEN] Should return variant description
        SimpleAssert.AreEqual(ExpectedDescription, Result, 'Should return variant description when variant exists');
        
        // Cleanup
        CleanupTestData(Item."No.", ItemVariant.Code);
    end;

    [Test]
    procedure TestGetItemDescription_WithValidItemNoVariant()
    var
        Item: Record Item;
        Result: Text[100];
        ExpectedDescription: Text[100];
    begin
        // [GIVEN] An item exists with description but no variant code provided
        CreateTestItem(Item);
        ExpectedDescription := Item.Description;

        // [WHEN] Getting item description without variant
        Result := MaxwellBasicFunctions.GetItemDescription(Item."No.", '');

        // [THEN] Should return item description
        SimpleAssert.AreEqual(ExpectedDescription, Result, 'Should return item description when no variant provided');
        
        // Cleanup
        CleanupTestData(Item."No.", '');
    end;

    [Test]
    procedure TestGetItemDescription_WithInvalidItem()
    var
        Result: Text[100];
        ItemNoNotExists: Code[20];
    begin
        // [GIVEN] Item does not exist
        ItemNoNotExists := 'NONEXISTENT';

        // [WHEN] Getting item description for non-existent item
        Result := MaxwellBasicFunctions.GetItemDescription(ItemNoNotExists, '');

        // [THEN] Should return empty string
        SimpleAssert.AreEqual('', Result, 'Should return empty string for non-existent item');
    end;

    [Test]
    procedure TestIsLotExpired_WithExpiredLot()
    var
        Item: Record Item;
        LotNoInformation: Record "Lot No. Information";
        Result: Boolean;
    begin
        // [GIVEN] A lot with expiration date in the past
        CreateTestItem(Item);
        CreateExpiredLot(LotNoInformation, Item."No.", '', 'EXPIREDLOT001');

        // [WHEN] Checking if lot is expired
        Result := MaxwellBasicFunctions.IsLotExpired(Item."No.", '', 'EXPIREDLOT001');

        // [THEN] Should return true
        SimpleAssert.IsTrue(Result, 'Should return true for expired lot');
        
        // Cleanup
        CleanupTestData(Item."No.", '');
        CleanupLotData('EXPIREDLOT001');
    end;

    [Test]
    procedure TestIsLotExpired_WithValidLot()
    var
        Item: Record Item;
        LotNoInformation: Record "Lot No. Information";
        Result: Boolean;
    begin
        // [GIVEN] A lot with expiration date in the future
        CreateTestItem(Item);
        CreateValidLot(LotNoInformation, Item."No.", '', 'VALIDLOT001');

        // [WHEN] Checking if lot is expired
        Result := MaxwellBasicFunctions.IsLotExpired(Item."No.", '', 'VALIDLOT001');

        // [THEN] Should return false
        SimpleAssert.IsFalse(Result, 'Should return false for valid lot');
        
        // Cleanup
        CleanupTestData(Item."No.", '');
        CleanupLotData('VALIDLOT001');
    end;

    [Test]
    procedure TestIsLotExpired_WithEmptyLotNo()
    var
        Result: Boolean;
    begin
        // [GIVEN] Empty lot number
        
        // [WHEN] Checking if empty lot is expired
        Result := MaxwellBasicFunctions.IsLotExpired('ANYITEM', '', '');

        // [THEN] Should return false
        SimpleAssert.IsFalse(Result, 'Should return false for empty lot number');
    end;

    [Test]
    procedure TestIsPackageExpired_WithExpiredPackage()
    var
        Item: Record Item;
        PackageNoInformation: Record "Package No. Information";
        Result: Boolean;
    begin
        // [GIVEN] A package with expiration date in the past
        CreateTestItem(Item);
        CreateExpiredPackage(PackageNoInformation, Item."No.", '', 'EXPIREDPKG001');

        // [WHEN] Checking if package is expired
        Result := MaxwellBasicFunctions.IsPackageExpired(Item."No.", '', 'EXPIREDPKG001');

        // [THEN] Should return true
        SimpleAssert.IsTrue(Result, 'Should return true for expired package');
        
        // Cleanup
        CleanupTestData(Item."No.", '');
        CleanupPackageData('EXPIREDPKG001');
    end;

    [Test]
    procedure TestIsPackageExpired_WithValidPackage()
    var
        Item: Record Item;
        PackageNoInformation: Record "Package No. Information";
        Result: Boolean;
    begin
        // [GIVEN] A package with expiration date in the future
        CreateTestItem(Item);
        CreateValidPackage(PackageNoInformation, Item."No.", '', 'VALIDPKG001');

        // [WHEN] Checking if package is expired
        Result := MaxwellBasicFunctions.IsPackageExpired(Item."No.", '', 'VALIDPKG001');

        // [THEN] Should return false
        SimpleAssert.IsFalse(Result, 'Should return false for valid package');
        
        // Cleanup
        CleanupTestData(Item."No.", '');
        CleanupPackageData('VALIDPKG001');
    end;

    [Test]
    procedure TestIsPackageExpired_WithEmptyPackageNo()
    var
        Result: Boolean;
    begin
        // [GIVEN] Empty package number
        
        // [WHEN] Checking if empty package is expired
        Result := MaxwellBasicFunctions.IsPackageExpired('ANYITEM', '', '');

        // [THEN] Should return false
        SimpleAssert.IsFalse(Result, 'Should return false for empty package number');
    end;

    // Helper procedures for test setup
    local procedure CreateTestItem(var Item: Record Item)
    begin
        Item.Init();
        Item."No." := 'TESTITEM' + Format(Random(999999));
        Item.Description := 'Test Item Description';
        Item.Insert(true);
    end;

    local procedure CreateTestItemVariant(var ItemVariant: Record "Item Variant"; ItemNo: Code[20])
    begin
        ItemVariant.Init();
        ItemVariant."Item No." := ItemNo;
        ItemVariant.Code := 'VAR001';
        ItemVariant.Description := 'Test Variant Description';
        ItemVariant.Insert(true);
    end;

    local procedure CreateExpiredLot(var LotNoInformation: Record "Lot No. Information"; ItemNo: Code[20]; VariantCode: Code[10]; LotNo: Code[50])
    begin
        LotNoInformation.Init();
        LotNoInformation."Item No." := ItemNo;
        LotNoInformation."Variant Code" := VariantCode;
        LotNoInformation."Lot No." := LotNo;
        LotNoInformation."Expiration Date MXW" := CalcDate('<-1D>', Today()); // Yesterday
        LotNoInformation.Insert(true);
    end;

    local procedure CreateValidLot(var LotNoInformation: Record "Lot No. Information"; ItemNo: Code[20]; VariantCode: Code[10]; LotNo: Code[50])
    begin
        LotNoInformation.Init();
        LotNoInformation."Item No." := ItemNo;
        LotNoInformation."Variant Code" := VariantCode;
        LotNoInformation."Lot No." := LotNo;
        LotNoInformation."Expiration Date MXW" := CalcDate('<+30D>', Today()); // 30 days from now
        LotNoInformation.Insert(true);
    end;

    local procedure CreateExpiredPackage(var PackageNoInformation: Record "Package No. Information"; ItemNo: Code[20]; VariantCode: Code[10]; PackageNo: Code[50])
    begin
        PackageNoInformation.Init();
        PackageNoInformation."Item No." := ItemNo;
        PackageNoInformation."Variant Code" := VariantCode;
        PackageNoInformation."Package No." := PackageNo;
        PackageNoInformation."Expiration Date MXW" := CalcDate('<-1D>', Today()); // Yesterday
        PackageNoInformation.Insert(true);
    end;

    local procedure CreateValidPackage(var PackageNoInformation: Record "Package No. Information"; ItemNo: Code[20]; VariantCode: Code[10]; PackageNo: Code[50])
    begin
        PackageNoInformation.Init();
        PackageNoInformation."Item No." := ItemNo;
        PackageNoInformation."Variant Code" := VariantCode;
        PackageNoInformation."Package No." := PackageNo;
        PackageNoInformation."Expiration Date MXW" := CalcDate('<+30D>', Today()); // 30 days from now
        PackageNoInformation.Insert(true);
    end;

    // Helper procedures for cleanup
    local procedure CleanupTestData(ItemNo: Code[20]; VariantCode: Code[10])
    var
        Item: Record Item;
        ItemVariant: Record "Item Variant";
    begin
        if VariantCode <> '' then
            if ItemVariant.Get(ItemNo, VariantCode) then
                ItemVariant.Delete(true);
                
        if Item.Get(ItemNo) then
            Item.Delete(true);
    end;

    local procedure CleanupLotData(LotNo: Code[50])
    var
        LotNoInformation: Record "Lot No. Information";
    begin
        LotNoInformation.SetRange("Lot No.", LotNo);
        if not LotNoInformation.IsEmpty() then
            LotNoInformation.DeleteAll(true);
    end;

    local procedure CleanupPackageData(PackageNo: Code[50])
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        PackageNoInformation.SetRange("Package No.", PackageNo);
        if not PackageNoInformation.IsEmpty() then
            PackageNoInformation.DeleteAll(true);
    end;
}