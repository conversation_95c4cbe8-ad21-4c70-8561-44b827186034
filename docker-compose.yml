version: '3.8'

services:
  maxwell-bc:
    image: mcr.microsoft.com/businesscentral/onprem:26.0
    container_name: maxwell-bc-container
    ports:
      - "8080:8080"
      - "7046:7046"
      - "7047:7047"
      - "7048:7048"
      - "7049:7049"
    environment:
      - accept_eula=Y
      - username=admin
      - password=P@ssw0rd123!
      - auth=UserPassword
      - updateHosts=true
      - useSSL=false
      - includeTestToolkit=true
      - enableSymbolLoading=true
      - shortcuts=None
      - databaseServer=localhost
      - databaseName=maxwell-bc-db
      - multitenant=false
      - assignPremiumPlan=false
    volumes:
      - bc-data:/data
      - ./output:/apps
      - ./.dependencies:/dependencies
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "powershell", "-Command", "try { Invoke-WebRequest -Uri 'http://localhost:8080/BC/' -UseBasicParsing; exit 0 } catch { exit 1 }"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G

volumes:
  bc-data:
    driver: local