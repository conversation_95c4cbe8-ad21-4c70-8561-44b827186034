codeunit 61002 "Maxwell Test Runner MXW"
{
    Subtype = TestRunner;

    trigger OnRun()
    begin
        // Run all Maxwell test codeunits
        Codeunit.Run(Codeunit::"Maxwell Basic Functions Test");
    end;

    trigger OnBeforeTestRun(CodeunitID: Integer; CodeunitName: Text; FunctionName: Text; FunctionTestPermissions: TestPermissions): Boolean
    begin
        // Log the start of test execution
        Message('Starting test: %1.%2', CodeunitName, FunctionName);
    end;

    trigger OnAfterTestRun(CodeunitID: Integer; CodeunitName: Text; FunctionName: Text; FunctionTestPermissions: TestPermissions; IsSuccess: Boolean)
    begin
        // Log the result of test execution
        if IsSuccess then
            Message('Test PASSED: %1.%2', CodeunitName, FunctionName)
        else
            Message('Test FAILED: %1.%2', CodeunitName, FunctionName);
    end;
}