# Maxwell Customizations - Dependency Management Script
# This script manages local installation and setup of all required dependencies

param(
    [Parameter(Mandatory=$false)]
    [switch]$Update,
    
    [Parameter(Mandatory=$false)]
    [switch]$Clean,
    
    [Parameter(Mandatory=$false)]
    [switch]$ValidateOnly
)

# Global configuration
$Global:SettingsFile = ".\settings.json"
$Global:DependenciesPath = ".\.dependencies"
$Global:AlPackagesPath = ".\.alpackages"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $(
        switch($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "INFO" { "Cyan" }
            default { "White" }
        }
    )
}

function Initialize-Environment {
    Write-Log "Initializing Maxwell Customizations development environment"
    
    # Create required directories
    @($Global:DependenciesPath, $Global:AlPackagesPath, ".\output") | ForEach-Object {
        if (-not (Test-Path $_)) {
            Write-Log "Creating directory: $_"
            New-Item -ItemType Directory -Path $_ -Force | Out-Null
        }
    }
    
    # Check if Git is available
    try {
        git --version | Out-Null
        Write-Log "Git is available" -Level "SUCCESS"
    }
    catch {
        Write-Log "Git is not available. Some dependency operations may fail." -Level "WARN"
    }
    
    # Check if AL Extension is available
    try {
        $alExtension = code --list-extensions | Where-Object { $_ -like "*ms-dynamics-smb.al*" }
        if ($alExtension) {
            Write-Log "AL Language Extension is installed" -Level "SUCCESS"
        } else {
            Write-Log "AL Language Extension not found. Please install it from VS Code marketplace." -Level "WARN"
        }
    }
    catch {
        Write-Log "Could not check VS Code extensions" -Level "WARN"
    }
}

function Read-Settings {
    if (-not (Test-Path $Global:SettingsFile)) {
        Write-Log "settings.json not found. Creating default settings file." -Level "WARN"
        return $null
    }
    
    try {
        $settings = Get-Content $Global:SettingsFile -Raw | ConvertFrom-Json
        Write-Log "Loaded settings from $Global:SettingsFile"
        return $settings
    }
    catch {
        Write-Log "Failed to parse settings.json: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function Clean-Dependencies {
    Write-Log "Cleaning dependencies folder"
    
    if (Test-Path $Global:DependenciesPath) {
        Remove-Item -Path $Global:DependenciesPath -Recurse -Force
        Write-Log "Removed dependencies folder" -Level "SUCCESS"
    }
    
    if (Test-Path $Global:AlPackagesPath) {
        Remove-Item -Path $Global:AlPackagesPath -Recurse -Force
        Write-Log "Removed AL packages cache" -Level "SUCCESS"
    }
}

function Install-Dependency {
    param(
        [object]$Dependency,
        [bool]$ForceUpdate = $false
    )
    
    $depName = $Dependency.name
    $localPath = $Dependency.localPath
    
    if (-not $localPath) {
        $localPath = Join-Path $Global:DependenciesPath $depName.Replace(" ", "").Replace("/", "").Replace("\", "")
    }
    
    Write-Log "Processing dependency: $depName"
    Write-Log "Local path: $localPath"
    
    # Create local path if it doesn't exist
    if (-not (Test-Path $localPath)) {
        New-Item -ItemType Directory -Path $localPath -Force | Out-Null
    }
    
    # Handle GitHub repository
    if ($Dependency.githubRepo) {
        $repoUrl = $Dependency.githubRepo
        Write-Log "GitHub repository: $repoUrl"
        
        # Check if it's already cloned
        $gitFolder = Join-Path $localPath ".git"
        
        if ((Test-Path $gitFolder) -and -not $ForceUpdate) {
            Write-Log "Repository already cloned. Use -Update to refresh." -Level "INFO"
            
            if ($Update) {
                Write-Log "Updating repository..."
                Push-Location $localPath
                try {
                    git pull origin main 2>$null
                    if ($LASTEXITCODE -ne 0) {
                        git pull origin master 2>$null
                    }
                    Write-Log "Repository updated successfully" -Level "SUCCESS"
                }
                catch {
                    Write-Log "Failed to update repository: $($_.Exception.Message)" -Level "ERROR"
                }
                finally {
                    Pop-Location
                }
            }
        }
        else {
            # Clone the repository
            Write-Log "Cloning repository..."
            try {
                if (Test-Path $localPath) {
                    Remove-Item -Path $localPath -Recurse -Force
                }
                
                git clone $repoUrl $localPath 2>$null
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Log "Successfully cloned: $depName" -Level "SUCCESS"
                } else {
                    throw "Git clone failed with exit code $LASTEXITCODE"
                }
            }
            catch {
                Write-Log "Failed to clone repository: $($_.Exception.Message)" -Level "ERROR"
                
                # Create a placeholder structure
                Write-Log "Creating placeholder structure for $depName"
                if (-not (Test-Path $localPath)) {
                    New-Item -ItemType Directory -Path $localPath -Force | Out-Null
                }
                
                # Create placeholder app.json
                $placeholderApp = @{
                    id = $Dependency.id
                    name = $Dependency.name
                    publisher = $Dependency.publisher
                    version = $Dependency.version
                    dependencies = @()
                }
                
                $appJsonPath = Join-Path $localPath "app.json"
                $placeholderApp | ConvertTo-Json -Depth 3 | Out-File -FilePath $appJsonPath -Encoding UTF8
                Write-Log "Created placeholder app.json for $depName" -Level "WARN"
            }
        }
    }
    
    # Handle direct download URL
    if ($Dependency.downloadUrl) {
        Write-Log "Downloading from URL: $($Dependency.downloadUrl)"
        try {
            $fileName = "$($depName.Replace(' ', '_'))_$($Dependency.version).app"
            $filePath = Join-Path $localPath $fileName
            
            Invoke-WebRequest -Uri $Dependency.downloadUrl -OutFile $filePath
            Write-Log "Successfully downloaded: $fileName" -Level "SUCCESS"
        }
        catch {
            Write-Log "Failed to download from URL: $($_.Exception.Message)" -Level "ERROR"
        }
    }
    
    # Validate dependency structure
    $appJsonPath = Join-Path $localPath "app.json"
    if (Test-Path $appJsonPath) {
        try {
            $depAppInfo = Get-Content $appJsonPath -Raw | ConvertFrom-Json
            Write-Log "Dependency validation - Name: $($depAppInfo.name), Version: $($depAppInfo.version)" -Level "SUCCESS"
        }
        catch {
            Write-Log "Warning: Could not validate dependency app.json" -Level "WARN"
        }
    }
}

function Validate-Dependencies {
    param([object]$Settings)
    
    Write-Log "Validating dependencies..."
    
    if (-not $Settings -or -not $Settings.dependencies -or -not $Settings.dependencies.appDependencies) {
        Write-Log "No dependencies defined in settings.json" -Level "WARN"
        return $false
    }
    
    $allValid = $true
    
    foreach ($dependency in $Settings.dependencies.appDependencies) {
        $localPath = $dependency.localPath
        if (-not $localPath) {
            $localPath = Join-Path $Global:DependenciesPath $dependency.name.Replace(" ", "").Replace("/", "").Replace("\", "")
        }
        
        Write-Log "Validating: $($dependency.name)"
        
        # Check if local path exists
        if (-not (Test-Path $localPath)) {
            Write-Log "Missing dependency: $($dependency.name) at $localPath" -Level "ERROR"
            $allValid = $false
            continue
        }
        
        # Check for app.json
        $appJsonPath = Join-Path $localPath "app.json"
        if (-not (Test-Path $appJsonPath)) {
            Write-Log "Missing app.json for: $($dependency.name)" -Level "ERROR"
            $allValid = $false
            continue
        }
        
        # Validate app.json content
        try {
            $depAppInfo = Get-Content $appJsonPath -Raw | ConvertFrom-Json
            
            if ($depAppInfo.id -ne $dependency.id) {
                Write-Log "ID mismatch for $($dependency.name): expected $($dependency.id), found $($depAppInfo.id)" -Level "ERROR"
                $allValid = $false
            }
            
            if ($depAppInfo.version -ne $dependency.version) {
                Write-Log "Version mismatch for $($dependency.name): expected $($dependency.version), found $($depAppInfo.version)" -Level "WARN"
            }
            
            Write-Log "✓ Valid: $($dependency.name)" -Level "SUCCESS"
        }
        catch {
            Write-Log "Invalid app.json for: $($dependency.name)" -Level "ERROR"
            $allValid = $false
        }
    }
    
    return $allValid
}

function Show-DependencyStatus {
    param([object]$Settings)
    
    Write-Log "=== Dependency Status ==="
    
    if (-not $Settings -or -not $Settings.dependencies -or -not $Settings.dependencies.appDependencies) {
        Write-Log "No dependencies configured" -Level "INFO"
        return
    }
    
    foreach ($dependency in $Settings.dependencies.appDependencies) {
        $localPath = $dependency.localPath
        if (-not $localPath) {
            $localPath = Join-Path $Global:DependenciesPath $dependency.name.Replace(" ", "").Replace("/", "").Replace("\", "")
        }
        
        Write-Log "Dependency: $($dependency.name) v$($dependency.version)"
        Write-Log "  ID: $($dependency.id)"
        Write-Log "  Publisher: $($dependency.publisher)"
        Write-Log "  Local Path: $localPath"
        Write-Log "  GitHub Repo: $($dependency.githubRepo)"
        Write-Log "  Status: $(if (Test-Path $localPath) { 'Installed' } else { 'Missing' })"
        Write-Log ""
    }
}

function Main {
    try {
        Write-Log "Maxwell Customizations - Dependency Management Script"
        Write-Log "======================================================"
        
        if ($Clean) {
            Clean-Dependencies
            if (-not $ValidateOnly) {
                Write-Log "Clean completed. Run without -Clean to reinstall dependencies." -Level "SUCCESS"
                return
            }
        }
        
        # Initialize environment
        Initialize-Environment
        
        # Read settings
        $settings = Read-Settings
        
        if (-not $settings) {
            Write-Log "Cannot proceed without valid settings.json" -Level "ERROR"
            exit 1
        }
        
        # Show current status
        Show-DependencyStatus -Settings $settings
        
        if ($ValidateOnly) {
            $isValid = Validate-Dependencies -Settings $settings
            if ($isValid) {
                Write-Log "All dependencies are valid!" -Level "SUCCESS"
                exit 0
            } else {
                Write-Log "Some dependencies are invalid or missing!" -Level "ERROR"
                exit 1
            }
        }
        
        # Install dependencies
        if ($settings.dependencies -and $settings.dependencies.appDependencies) {
            Write-Log "Installing dependencies..."
            
            foreach ($dependency in $settings.dependencies.appDependencies) {
                Install-Dependency -Dependency $dependency -ForceUpdate $Update
            }
            
            # Final validation
            $isValid = Validate-Dependencies -Settings $settings
            if ($isValid) {
                Write-Log "All dependencies installed and validated successfully!" -Level "SUCCESS"
            } else {
                Write-Log "Some dependencies failed validation. Check the output above." -Level "WARN"
            }
        } else {
            Write-Log "No dependencies to install" -Level "INFO"
        }
        
        Write-Log "Dependency management completed!" -Level "SUCCESS"
        Write-Log ""
        Write-Log "Next steps:"
        Write-Log "1. Open VS Code in this folder"
        Write-Log "2. Run 'AL: Download Symbols' command"
        Write-Log "3. Use 'AL: Package' to build the extension"
        Write-Log "4. Use '.\PublishAppToDocker.ps1' to publish to Docker"
        
    }
    catch {
        Write-Log "Script execution failed: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
}

# Execute main function
Main