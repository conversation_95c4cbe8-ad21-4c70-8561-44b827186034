# Maxwell Customizations - Dependency Management Script
# This script manages local installation and setup of all required dependencies
# Including Business Central Online Sandbox testing capabilities

param(
    [Parameter(Mandatory=$false)]
    [switch]$Update,

    [Parameter(Mandatory=$false)]
    [switch]$Clean,

    [Parameter(Mandatory=$false)]
    [switch]$ValidateOnly,

    [Parameter(Mandatory=$false)]
    [switch]$SetupOnlineTestEnvironment,

    [Parameter(Mandatory=$false)]
    [string]$BCEnvironmentName,

    [Parameter(Mandatory=$false)]
    [string]$TenantId
)

# Global configuration
$Global:SettingsFile = ".\settings.json"
$Global:DependenciesPath = ".\.dependencies"
$Global:AlPackagesPath = ".\.alpackages"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $(
        switch($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "INFO" { "Cyan" }
            default { "White" }
        }
    )
}

function Initialize-Environment {
    Write-Log "Initializing Maxwell Customizations development environment"

    # Create required directories
    @($Global:DependenciesPath, $Global:AlPackagesPath, ".\output") | ForEach-Object {
        if (-not (Test-Path $_)) {
            Write-Log "Creating directory: $_"
            New-Item -ItemType Directory -Path $_ -Force | Out-Null
        }
    }

    # Check if Git is available
    try {
        git --version | Out-Null
        Write-Log "Git is available" -Level "SUCCESS"
    }
    catch {
        Write-Log "Git is not available. Some dependency operations may fail." -Level "WARN"
    }

    # Check if AL Extension is available
    try {
        $alExtension = code --list-extensions | Where-Object { $_ -like "*ms-dynamics-smb.al*" }
        if ($alExtension) {
            Write-Log "AL Language Extension is installed" -Level "SUCCESS"
        } else {
            Write-Log "AL Language Extension not found. Please install it from VS Code marketplace." -Level "WARN"
        }
    }
    catch {
        Write-Log "Could not check VS Code extensions" -Level "WARN"
    }
}

function Read-Settings {
    if (-not (Test-Path $Global:SettingsFile)) {
        Write-Log "settings.json not found. Creating default settings file." -Level "WARN"
        return $null
    }

    try {
        $settings = Get-Content $Global:SettingsFile -Raw | ConvertFrom-Json
        Write-Log "Loaded settings from $Global:SettingsFile"
        return $settings
    }
    catch {
        Write-Log "Failed to parse settings.json: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function Clean-Dependencies {
    Write-Log "Cleaning dependencies folder"

    if (Test-Path $Global:DependenciesPath) {
        Remove-Item -Path $Global:DependenciesPath -Recurse -Force
        Write-Log "Removed dependencies folder" -Level "SUCCESS"
    }

    if (Test-Path $Global:AlPackagesPath) {
        Remove-Item -Path $Global:AlPackagesPath -Recurse -Force
        Write-Log "Removed AL packages cache" -Level "SUCCESS"
    }
}

function Install-Dependency {
    param(
        [object]$Dependency,
        [bool]$ForceUpdate = $false
    )

    $depName = $Dependency.name
    $localPath = $Dependency.localPath

    if (-not $localPath) {
        $localPath = Join-Path $Global:DependenciesPath $depName.Replace(" ", "").Replace("/", "").Replace("\", "")
    }

    Write-Log "Processing dependency: $depName"
    Write-Log "Local path: $localPath"

    # Create local path if it doesn't exist
    if (-not (Test-Path $localPath)) {
        New-Item -ItemType Directory -Path $localPath -Force | Out-Null
    }

    # Handle GitHub repository
    if ($Dependency.githubRepo) {
        $repoUrl = $Dependency.githubRepo
        Write-Log "GitHub repository: $repoUrl"

        # Check if it's already cloned
        $gitFolder = Join-Path $localPath ".git"

        if ((Test-Path $gitFolder) -and -not $ForceUpdate) {
            Write-Log "Repository already cloned. Use -Update to refresh." -Level "INFO"

            if ($Update) {
                Write-Log "Updating repository..."
                Push-Location $localPath
                try {
                    git pull origin main 2>$null
                    if ($LASTEXITCODE -ne 0) {
                        git pull origin master 2>$null
                    }
                    Write-Log "Repository updated successfully" -Level "SUCCESS"
                }
                catch {
                    Write-Log "Failed to update repository: $($_.Exception.Message)" -Level "ERROR"
                }
                finally {
                    Pop-Location
                }
            }
        }
        else {
            # Clone the repository
            Write-Log "Cloning repository..."
            try {
                if (Test-Path $localPath) {
                    Remove-Item -Path $localPath -Recurse -Force
                }

                git clone $repoUrl $localPath 2>$null

                if ($LASTEXITCODE -eq 0) {
                    Write-Log "Successfully cloned: $depName" -Level "SUCCESS"
                } else {
                    throw "Git clone failed with exit code $LASTEXITCODE"
                }
            }
            catch {
                Write-Log "Failed to clone repository: $($_.Exception.Message)" -Level "ERROR"

                # Create a placeholder structure
                Write-Log "Creating placeholder structure for $depName"
                if (-not (Test-Path $localPath)) {
                    New-Item -ItemType Directory -Path $localPath -Force | Out-Null
                }

                # Create placeholder app.json
                $placeholderApp = @{
                    id = $Dependency.id
                    name = $Dependency.name
                    publisher = $Dependency.publisher
                    version = $Dependency.version
                    dependencies = @()
                }

                $appJsonPath = Join-Path $localPath "app.json"
                $placeholderApp | ConvertTo-Json -Depth 3 | Out-File -FilePath $appJsonPath -Encoding UTF8
                Write-Log "Created placeholder app.json for $depName" -Level "WARN"
            }
        }
    }

    # Handle direct download URL
    if ($Dependency.downloadUrl) {
        Write-Log "Downloading from URL: $($Dependency.downloadUrl)"
        try {
            $fileName = "$($depName.Replace(' ', '_'))_$($Dependency.version).app"
            $filePath = Join-Path $localPath $fileName

            Invoke-WebRequest -Uri $Dependency.downloadUrl -OutFile $filePath
            Write-Log "Successfully downloaded: $fileName" -Level "SUCCESS"
        }
        catch {
            Write-Log "Failed to download from URL: $($_.Exception.Message)" -Level "ERROR"
        }
    }

    # Validate dependency structure
    $appJsonPath = Join-Path $localPath "app.json"
    if (Test-Path $appJsonPath) {
        try {
            $depAppInfo = Get-Content $appJsonPath -Raw | ConvertFrom-Json
            Write-Log "Dependency validation - Name: $($depAppInfo.name), Version: $($depAppInfo.version)" -Level "SUCCESS"
        }
        catch {
            Write-Log "Warning: Could not validate dependency app.json" -Level "WARN"
        }
    }
}

function Validate-Dependencies {
    param([object]$Settings)

    Write-Log "Validating dependencies..."

    if (-not $Settings -or -not $Settings.dependencies -or -not $Settings.dependencies.appDependencies) {
        Write-Log "No dependencies defined in settings.json" -Level "WARN"
        return $false
    }

    $allValid = $true

    foreach ($dependency in $Settings.dependencies.appDependencies) {
        $localPath = $dependency.localPath
        if (-not $localPath) {
            $localPath = Join-Path $Global:DependenciesPath $dependency.name.Replace(" ", "").Replace("/", "").Replace("\", "")
        }

        Write-Log "Validating: $($dependency.name)"

        # Check if local path exists
        if (-not (Test-Path $localPath)) {
            Write-Log "Missing dependency: $($dependency.name) at $localPath" -Level "ERROR"
            $allValid = $false
            continue
        }

        # Check for app.json
        $appJsonPath = Join-Path $localPath "app.json"
        if (-not (Test-Path $appJsonPath)) {
            Write-Log "Missing app.json for: $($dependency.name)" -Level "ERROR"
            $allValid = $false
            continue
        }

        # Validate app.json content
        try {
            $depAppInfo = Get-Content $appJsonPath -Raw | ConvertFrom-Json

            if ($depAppInfo.id -ne $dependency.id) {
                Write-Log "ID mismatch for $($dependency.name): expected $($dependency.id), found $($depAppInfo.id)" -Level "ERROR"
                $allValid = $false
            }

            if ($depAppInfo.version -ne $dependency.version) {
                Write-Log "Version mismatch for $($dependency.name): expected $($dependency.version), found $($depAppInfo.version)" -Level "WARN"
            }

            Write-Log "✓ Valid: $($dependency.name)" -Level "SUCCESS"
        }
        catch {
            Write-Log "Invalid app.json for: $($dependency.name)" -Level "ERROR"
            $allValid = $false
        }
    }

    return $allValid
}

function Show-DependencyStatus {
    param([object]$Settings)

    Write-Log "=== Dependency Status ==="

    if (-not $Settings -or -not $Settings.dependencies -or -not $Settings.dependencies.appDependencies) {
        Write-Log "No dependencies configured" -Level "INFO"
        return
    }

    foreach ($dependency in $Settings.dependencies.appDependencies) {
        $localPath = $dependency.localPath
        if (-not $localPath) {
            $localPath = Join-Path $Global:DependenciesPath $dependency.name.Replace(" ", "").Replace("/", "").Replace("\", "")
        }

        Write-Log "Dependency: $($dependency.name) v$($dependency.version)"
        Write-Log "  ID: $($dependency.id)"
        Write-Log "  Publisher: $($dependency.publisher)"
        Write-Log "  Local Path: $localPath"
        Write-Log "  GitHub Repo: $($dependency.githubRepo)"
        Write-Log "  Status: $(if (Test-Path $localPath) { 'Installed' } else { 'Missing' })"
        Write-Log ""
    }
}

# ===== BUSINESS CENTRAL ONLINE TESTING FUNCTIONS =====

function Install-BcContainerHelper {
    Write-Log "Installing/Updating BcContainerHelper PowerShell module..."

    try {
        # Check if module is already installed
        $existingModule = Get-Module -ListAvailable -Name BcContainerHelper

        if ($existingModule) {
            Write-Log "BcContainerHelper is already installed. Version: $($existingModule.Version)" -Level "INFO"

            if ($Update) {
                Write-Log "Updating BcContainerHelper module..."
                Update-Module -Name BcContainerHelper -Force
                Write-Log "BcContainerHelper updated successfully" -Level "SUCCESS"
            }
        } else {
            Write-Log "Installing BcContainerHelper module..."
            Install-Module -Name BcContainerHelper -Force -AllowClobber
            Write-Log "BcContainerHelper installed successfully" -Level "SUCCESS"
        }

        # Import the module
        Import-Module BcContainerHelper -Force
        Write-Log "BcContainerHelper module imported" -Level "SUCCESS"

        return $true
    }
    catch {
        Write-Log "Failed to install/update BcContainerHelper: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Setup-BCAuthContext {
    param(
        [Parameter(Mandatory=$true)]
        [string]$TenantId,

        [Parameter(Mandatory=$false)]
        [string]$RefreshToken
    )

    Write-Log "Setting up Business Central authentication context..."

    try {
        if ($RefreshToken) {
            Write-Log "Using provided refresh token for authentication"
            $authContext = New-BcAuthContext -refreshToken $RefreshToken
        } else {
            Write-Log "Interactive authentication required. Please sign in when prompted."
            $authContext = New-BcAuthContext -includeDeviceLogin
        }

        if ($authContext) {
            Write-Log "Authentication context created successfully" -Level "SUCCESS"
            return $authContext
        } else {
            throw "Failed to create authentication context"
        }
    }
    catch {
        Write-Log "Failed to setup BC authentication context: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function Install-TestRunnerApp {
    param(
        [Parameter(Mandatory=$true)]
        [object]$BcAuthContext,

        [Parameter(Mandatory=$true)]
        [string]$Environment,

        [Parameter(Mandatory=$false)]
        [string]$ContainerName = "bcserver"
    )

    Write-Log "Installing Test Runner app from AppSource..."

    try {
        # Test Runner App ID from the blog post
        $testRunnerAppId = '23de40a6-dfe8-4f80-80db-d70f83ce8caf'

        Write-Log "Installing Test Runner app (ID: $testRunnerAppId)..."

        Install-BcAppFromAppSource `
            -containerName $ContainerName `
            -bcAuthContext $BcAuthContext `
            -environment $Environment `
            -appId $testRunnerAppId `
            -appName 'Test Runner'

        Write-Log "Test Runner app installed successfully" -Level "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to install Test Runner app: $($_.Exception.Message)" -Level "ERROR"
        Write-Log "Note: This requires a proxy container. You may need to create a FilesOnly container first." -Level "WARN"
        return $false
    }
}

function Install-TestFramework {
    param(
        [Parameter(Mandatory=$true)]
        [object]$BcAuthContext,

        [Parameter(Mandatory=$true)]
        [string]$Environment,

        [Parameter(Mandatory=$false)]
        [string]$ContainerName = "bcserver"
    )

    Write-Log "Installing Test Framework (Test Runner only)..."

    try {
        Import-TestToolkitToBcContainer `
            -containerName $ContainerName `
            -bcAuthContext $BcAuthContext `
            -environment $Environment `
            -includeTestRunnerOnly

        Write-Log "Test Framework installed successfully" -Level "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to install Test Framework: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Test-OnlineEnvironmentSetup {
    param(
        [Parameter(Mandatory=$true)]
        [object]$BcAuthContext,

        [Parameter(Mandatory=$true)]
        [string]$Environment
    )

    Write-Log "Testing Business Central Online environment setup..."

    try {
        # Get installed extensions to verify access
        $extensions = Get-BcInstalledExtensions -bcAuthContext $BcAuthContext -environment $Environment

        Write-Log "Successfully connected to environment: $Environment" -Level "SUCCESS"
        Write-Log "Found $($extensions.Count) installed extensions" -Level "INFO"

        # Check if Test Runner is installed
        $testRunner = $extensions | Where-Object { $_.displayname -like "*Test Runner*" }
        if ($testRunner) {
            Write-Log "Test Runner app is installed and ready" -Level "SUCCESS"
        } else {
            Write-Log "Test Runner app not found. You may need to install it manually." -Level "WARN"
        }

        return $true
    }
    catch {
        Write-Log "Failed to connect to online environment: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Main {
    try {
        Write-Log "Maxwell Customizations - Dependency Management Script"
        Write-Log "======================================================"

        if ($Clean) {
            Clean-Dependencies
            if (-not $ValidateOnly -and -not $SetupOnlineTestEnvironment) {
                Write-Log "Clean completed. Run without -Clean to reinstall dependencies." -Level "SUCCESS"
                return
            }
        }

        # Handle Business Central Online Test Environment Setup
        if ($SetupOnlineTestEnvironment) {
            Write-Log "Setting up Business Central Online Test Environment..."

            # Validate required parameters
            if (-not $BCEnvironmentName) {
                Write-Log "BCEnvironmentName parameter is required for online test setup" -Level "ERROR"
                Write-Log "Example: -BCEnvironmentName 'Maxwell-Sandbox'" -Level "INFO"
                exit 1
            }

            # Install BcContainerHelper
            $bcHelperInstalled = Install-BcContainerHelper
            if (-not $bcHelperInstalled) {
                Write-Log "Failed to install BcContainerHelper. Cannot proceed with online setup." -Level "ERROR"
                exit 1
            }

            # Setup authentication context
            Write-Log "Setting up authentication for Business Central Online..."
            Write-Log "You will be prompted to sign in to your Microsoft account." -Level "INFO"

            $authContext = Setup-BCAuthContext -TenantId $TenantId
            if (-not $authContext) {
                Write-Log "Failed to setup authentication context. Cannot proceed." -Level "ERROR"
                exit 1
            }

            # Test environment connection
            $connectionTest = Test-OnlineEnvironmentSetup -BcAuthContext $authContext -Environment $BCEnvironmentName
            if (-not $connectionTest) {
                Write-Log "Failed to connect to online environment. Please check your environment name and permissions." -Level "ERROR"
                exit 1
            }

            Write-Log "Business Central Online Test Environment setup completed!" -Level "SUCCESS"
            Write-Log ""
            Write-Log "Next steps for running tests online:"
            Write-Log "1. Use the created authentication context to run tests"
            Write-Log "2. Install Test Runner app if not already installed"
            Write-Log "3. Run tests using Run-TestsInBcContainer with -bcAuthContext and -environment parameters"
            Write-Log ""
            Write-Log "Example test command:"
            Write-Log "Run-TestsInBcContainer -containerName 'bcserver' -bcAuthContext `$authContext -environment '$BCEnvironmentName' -extensionId '<your-test-extension-id>' -detailed"

            return
        }

        # Initialize environment
        Initialize-Environment

        # Read settings
        $settings = Read-Settings

        if (-not $settings) {
            Write-Log "Cannot proceed without valid settings.json" -Level "ERROR"
            exit 1
        }

        # Show current status
        Show-DependencyStatus -Settings $settings

        if ($ValidateOnly) {
            $isValid = Validate-Dependencies -Settings $settings
            if ($isValid) {
                Write-Log "All dependencies are valid!" -Level "SUCCESS"
                exit 0
            } else {
                Write-Log "Some dependencies are invalid or missing!" -Level "ERROR"
                exit 1
            }
        }

        # Install dependencies
        if ($settings.dependencies -and $settings.dependencies.appDependencies) {
            Write-Log "Installing dependencies..."

            foreach ($dependency in $settings.dependencies.appDependencies) {
                Install-Dependency -Dependency $dependency -ForceUpdate $Update
            }

            # Final validation
            $isValid = Validate-Dependencies -Settings $settings
            if ($isValid) {
                Write-Log "All dependencies installed and validated successfully!" -Level "SUCCESS"
            } else {
                Write-Log "Some dependencies failed validation. Check the output above." -Level "WARN"
            }
        } else {
            Write-Log "No dependencies to install" -Level "INFO"
        }

        Write-Log "Dependency management completed!" -Level "SUCCESS"
        Write-Log ""
        Write-Log "Next steps:"
        Write-Log "1. Open VS Code in this folder"
        Write-Log "2. Run 'AL: Download Symbols' command"
        Write-Log "3. Use 'AL: Package' to build the extension"
        Write-Log "4. Use '.\PublishAppToDocker.ps1' to publish to Docker"
        Write-Log "5. For online testing, use: .\Setup-Dependencies.ps1 -SetupOnlineTestEnvironment -BCEnvironmentName 'YourEnvironment'"

    }
    catch {
        Write-Log "Script execution failed: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
}

# Execute main function
Main
