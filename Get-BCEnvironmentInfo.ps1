# Business Central Environment Information Helper
# This script helps you find your BC Online environment details

param(
    [Parameter(Mandatory=$false)]
    [string]$TenantId,
    
    [Parameter(Mandatory=$false)]
    [switch]$ShowAllEnvironments
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $(
        switch($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "INFO" { "Cyan" }
            default { "White" }
        }
    )
}

function Initialize-BcContainerHelper {
    Write-Log "Checking BcContainerHelper module..."
    
    try {
        $module = Get-Module -ListAvailable -Name BcContainerHelper
        if (-not $module) {
            Write-Log "Installing BcContainerHelper module..."
            Install-Module -Name BcContainerHelper -Force -AllowClobber
        }
        
        Import-Module BcContainerHelper -Force
        Write-Log "BcContainerHelper ready" -Level "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to initialize BcContainerHelper: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Get-AuthContext {
    param([string]$TenantId)
    
    Write-Log "Creating authentication context..."
    Write-Log "You will be prompted to sign in to your Microsoft account." -Level "INFO"
    
    try {
        if ($TenantId) {
            $authContext = New-BcAuthContext -includeDeviceLogin -tenantId $TenantId
        } else {
            $authContext = New-BcAuthContext -includeDeviceLogin
        }
        
        if ($authContext) {
            Write-Log "Authentication successful" -Level "SUCCESS"
            return $authContext
        } else {
            throw "Failed to create authentication context"
        }
    }
    catch {
        Write-Log "Authentication failed: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function Show-EnvironmentInfo {
    param([object]$AuthContext)
    
    Write-Log "Retrieving Business Central environment information..."
    
    try {
        # Get environments
        $environments = Get-BcEnvironments -bcAuthContext $AuthContext
        
        if (-not $environments -or $environments.Count -eq 0) {
            Write-Log "No Business Central environments found" -Level "WARN"
            return
        }
        
        Write-Log "Found $($environments.Count) Business Central environment(s)" -Level "SUCCESS"
        Write-Log ""
        Write-Log "=== BUSINESS CENTRAL ENVIRONMENTS ==="
        
        foreach ($env in $environments) {
            Write-Log "Environment Name: $($env.name)" -Level "INFO"
            Write-Log "  Display Name: $($env.displayName)"
            Write-Log "  Type: $($env.type)"
            Write-Log "  Country Code: $($env.countryCode)"
            Write-Log "  Version: $($env.version)"
            Write-Log "  Status: $($env.status)"
            Write-Log "  Web Client URL: $($env.webClientLoginUrl)"
            
            if ($ShowAllEnvironments -or $environments.Count -eq 1) {
                Write-Log "  --- Installed Extensions ---"
                try {
                    $extensions = Get-BcInstalledExtensions -bcAuthContext $AuthContext -environment $env.name
                    if ($extensions) {
                        $extensions | ForEach-Object {
                            Write-Log "    - $($_.displayName) (v$($_.version)) - ID: $($_.id)"
                        }
                    } else {
                        Write-Log "    No extensions found or access denied"
                    }
                }
                catch {
                    Write-Log "    Could not retrieve extensions: $($_.Exception.Message)" -Level "WARN"
                }
            }
            
            Write-Log ""
        }
        
        # Show recommended environment for testing
        $sandboxEnv = $environments | Where-Object { $_.type -eq "Sandbox" } | Select-Object -First 1
        if ($sandboxEnv) {
            Write-Log "=== RECOMMENDED FOR TESTING ===" -Level "SUCCESS"
            Write-Log "Environment Name: $($sandboxEnv.name)"
            Write-Log "Use this command to setup online testing:"
            Write-Log ".\Setup-Dependencies.ps1 -SetupOnlineTestEnvironment -BCEnvironmentName `"$($sandboxEnv.name)`""
            Write-Log ""
            Write-Log "Or use the batch file:"
            Write-Log "setup-online-testing.bat `"$($sandboxEnv.name)`""
        }
        
        return $environments
    }
    catch {
        Write-Log "Failed to retrieve environment information: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function Show-TenantInfo {
    param([object]$AuthContext)
    
    Write-Log "=== TENANT INFORMATION ==="
    
    try {
        # Extract tenant info from auth context if available
        if ($AuthContext.tenantId) {
            Write-Log "Tenant ID: $($AuthContext.tenantId)" -Level "INFO"
        }
        
        if ($AuthContext.scopes) {
            Write-Log "Scopes: $($AuthContext.scopes -join ', ')" -Level "INFO"
        }
        
        Write-Log ""
    }
    catch {
        Write-Log "Could not retrieve tenant information" -Level "WARN"
    }
}

function Main {
    try {
        Write-Log "Business Central Environment Information Helper"
        Write-Log "=============================================="
        
        # Initialize BcContainerHelper
        $bcHelperReady = Initialize-BcContainerHelper
        if (-not $bcHelperReady) {
            Write-Log "Cannot proceed without BcContainerHelper" -Level "ERROR"
            exit 1
        }
        
        # Get authentication context
        $authContext = Get-AuthContext -TenantId $TenantId
        if (-not $authContext) {
            Write-Log "Cannot proceed without authentication" -Level "ERROR"
            exit 1
        }
        
        # Show tenant information
        Show-TenantInfo -AuthContext $authContext
        
        # Show environment information
        $environments = Show-EnvironmentInfo -AuthContext $authContext
        
        if ($environments) {
            Write-Log "=== NEXT STEPS ===" -Level "SUCCESS"
            Write-Log "1. Choose a Sandbox environment from the list above"
            Write-Log "2. Run the setup script with your chosen environment name"
            Write-Log "3. Build and test your AL extensions"
            Write-Log ""
            Write-Log "For detailed instructions, see: ONLINE-TESTING-GUIDE.md"
        }
        
    }
    catch {
        Write-Log "Script execution failed: $($_.Exception.Message)" -Level "ERROR"
        exit 1
    }
}

# Execute main function
Main
