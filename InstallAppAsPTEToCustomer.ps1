# Maxwell Customizations - App Installation Script
# This script installs the Maxwell Customizations AL extension to Business Central

#Requires -Modules BcContainerHelper

[CmdletBinding()]
param()

# Function to get app information from app.json
function Get-BCAppInfoFromAppJson {
    param(
        [string]$WorkspacePath = $PWD
    )

    $appJsonPath = Join-Path $WorkspacePath "app.json"

    if (Test-Path $appJsonPath) {
        try {
            $appJson = Get-Content $appJsonPath | ConvertFrom-Json
            $appName = $appJson.name
            $appVersion = $appJson.version
            $appPublisher = $appJson.publisher

            # Generate .app file name
            $appFileName = "${appPublisher}_${appName}_${appVersion}.app"

            return @{
                Name = $appName
                Version = $appVersion
                Publisher = $appPublisher
                FileName = $appFileName
            }
        }
        catch {
            throw "Error reading app.json: $($_.Exception.Message)"
        }
    }

    throw "app.json file not found at: $appJsonPath"
}

# Read parameters from JSON file
Write-Host "Reading installation parameters..." -ForegroundColor Green
$parametersFile = Join-Path $PSScriptRoot "Parameters.json"

if (-not (Test-Path $parametersFile)) {
    Write-Error "Parameters.json file not found at: $parametersFile"
    exit 1
}

try {
    $parameters = Get-Content $parametersFile | ConvertFrom-Json
    Write-Host "Parameters loaded successfully" -ForegroundColor Green
}
catch {
    Write-Error "Error reading Parameters.json: $($_.Exception.Message)"
    exit 1
}

# Validate required parameters
if (-not $parameters.environment) {
    Write-Error "Environment parameter is missing in Parameters.json"
    exit 1
}

if (-not $parameters.appFiles -or $parameters.appFiles.Count -eq 0) {
    Write-Error "AppFiles parameter is missing or empty in Parameters.json"
    exit 1
}

# Verify app files exist
foreach ($appFile in $parameters.appFiles) {
    if (-not (Test-Path $appFile)) {
        Write-Error "App file not found: $appFile"
        exit 1
    }
    Write-Host "Found app file: $appFile" -ForegroundColor Green
}

# Get authentication context using device login
Write-Host "Authenticating with Business Central..." -ForegroundColor Yellow
try {
    # Parse tenant ID from environment parameter
    $targetTenant = ($parameters.environment -split '/')[0]
    Write-Host "Target Tenant ID: $targetTenant" -ForegroundColor Cyan

    # Authenticate to the specific tenant
    $authContext = New-BcAuthContext -includeDeviceLogin -tenantId $targetTenant
    Write-Host "Authentication successful" -ForegroundColor Green
}
catch {
    Write-Error "Authentication failed: $($_.Exception.Message)"
    exit 1
}

# Diagnostic: Check available environments
Write-Host "Checking available environments..." -ForegroundColor Cyan
try {
    $environments = Get-BcEnvironments -bcAuthContext $authContext
    Write-Host "Available environments:" -ForegroundColor Cyan
    foreach ($env in $environments) {
        $envInfo = "- Name: $($env.DisplayName), ID: $($env.EnvironmentId), Type: $($env.Type), Status: $($env.Status)"
        Write-Host $envInfo -ForegroundColor White

        # Check if this matches our target environment
        if ($env.EnvironmentId -eq ($parameters.environment -split '/')[0]) {
            Write-Host "  ✅ MATCH: This is our target environment!" -ForegroundColor Green
        }
    }
}
catch {
    Write-Warning "Could not retrieve environments: $($_.Exception.Message)"
}

# Diagnostic: Check tenant information
Write-Host "Checking tenant information..." -ForegroundColor Cyan
try {
    Write-Host "Authenticated Tenant ID: $($authContext.TenantId)" -ForegroundColor White
    Write-Host "Target Environment: $($parameters.environment)" -ForegroundColor White

    # Parse environment to check tenant match
    $targetTenant = ($parameters.environment -split '/')[0]
    if ($authContext.TenantId -eq $targetTenant) {
        Write-Host "✅ Tenant ID matches!" -ForegroundColor Green
    } else {
        Write-Host "❌ Tenant ID mismatch!" -ForegroundColor Red
        Write-Host "  Authenticated to: $($authContext.TenantId)" -ForegroundColor Red
        Write-Host "  Trying to access: $targetTenant" -ForegroundColor Red
    }
}
catch {
    Write-Warning "Could not check tenant information: $($_.Exception.Message)"
}

# Install apps
Write-Host "Publishing apps to environment: $($parameters.environment)" -ForegroundColor Yellow
try {
    Publish-PerTenantExtensionApps -bcAuthContext $authContext -environment $parameters.environment -appFiles $parameters.appFiles
    Write-Host "Apps published successfully!" -ForegroundColor Green
}
catch {
    Write-Error "Failed to publish apps: $($_.Exception.Message)"
    exit 1
}
