# Maxwell Customizations Test - Quick Start

Welcome to the **MaxwellCustomizationsTest** workspace! This is your dedicated testing environment for the Maxwell Customizations Business Central extension.

## 🚀 Getting Started

### 1. Open the Workspace
- Open VS Code
- File → Open Workspace from File
- Select `MaxwellCustomizationsTest.code-workspace`

### 2. Install Dependencies
```powershell
# In VS Code Terminal
AL: Download Symbols
```

### 3. Build the Test Project
```powershell
# In VS Code Terminal or use Ctrl+Shift+P
AL: Package
```

### 4. Deploy to Business Central
- Use F5 to deploy and run
- Or manually deploy the .app file to your sandbox

## 📁 Project Structure

```
MaxwellCustomizationsTest/
├── app.json                                 # Test project configuration
├── README.md                               # Detailed documentation
├── MaxwellCustomizationsTest.code-workspace # VS Code workspace file
├── .vscode/
│   └── launch.json                         # Debug configuration
└── src/
    ├── MaxwellBasicFunctionsTest.Codeunit.al    # Test codeunit
    └── MaxwellTestRunnerMXW.Codeunit.al         # Test runner
```

## ✅ What's Included

- **9 Test Methods** covering basic Maxwell functions
- **Test Runner** for automated execution
- **Proper Dependencies** on main extension and test libraries
- **Clean Test Data Management** with setup/teardown
- **VS Code Integration** with debugging support

## 🎯 Running Tests

### Option 1: VS Code Debug (Recommended)
1. Press F5 or use "Run and Debug"
2. Select "Maxwell Test: Publish"
3. Tests will run automatically when deployed

### Option 2: Business Central Test Tool
1. Open Business Central
2. Search for "Test Tool"
3. Run codeunit 61002 "Maxwell Test Runner MXW"

### Option 3: Individual Tests
1. Open Business Central
2. Search for "Test Tool"  
3. Run codeunit 61001 "Maxwell Basic Functions Test"

## 📊 Current Test Coverage

- ✅ Item Description Logic (3 scenarios)
- ✅ Lot Expiration Checking (3 scenarios)
- ✅ Package Expiration Checking (3 scenarios)

## 🔧 Prerequisites

Ensure your Business Central environment has:

1. **Maxwell Customizations extension** deployed
2. **Test Framework** installed:
   ```powershell
   Import-TestToolkitToBCContainer -containerName <your-container>
   ```

## 📝 Adding More Tests

1. Create new test methods in existing codeunits
2. Or create new test codeunits (use ID range 61000-61999)
3. Follow the existing patterns:
   - Use `[Test]` attribute
   - Given/When/Then structure
   - Proper cleanup in each test
   - LibraryAssert for validations

## 🐛 Troubleshooting

### Common Issues:
- **"Dependency not found"** → Deploy main extension first
- **"Library Assert not found"** → Install Test Framework
- **Compilation errors** → Run "AL: Download Symbols"

### Support:
- Check `README.md` for detailed documentation
- Review main extension is properly deployed
- Verify Test Framework installation

## 🎉 Success!

Once everything is set up, you should see test results in Business Central. Each test will show PASS/FAIL status with detailed messages.

Happy testing! 🧪