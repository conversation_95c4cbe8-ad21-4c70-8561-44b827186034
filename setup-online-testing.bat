@echo off
echo Maxwell Customizations - Online Testing Setup
echo =============================================
echo.

REM Check if environment name is provided
if "%1"=="" (
    echo Usage: setup-online-testing.bat "Environment-Name"
    echo Example: setup-online-testing.bat "Maxwell-Sandbox"
    echo.
    echo This will:
    echo - Install BcContainerHelper PowerShell module
    echo - Setup authentication for Business Central Online
    echo - Test connection to your environment
    echo.
    pause
    exit /b 1
)

set ENVIRONMENT_NAME=%1

echo Setting up online testing environment: %ENVIRONMENT_NAME%
echo.

REM Run the PowerShell setup script
powershell.exe -ExecutionPolicy Bypass -File "Setup-Dependencies.ps1" -SetupOnlineTestEnvironment -BCEnvironmentName "%ENVIRONMENT_NAME%"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Setup completed successfully!
    echo ========================================
    echo.
    echo Next steps:
    echo 1. Build your test app: AL: Package in VS Code
    echo 2. Run tests: .\Run-OnlineTests.ps1 -Environment "%ENVIRONMENT_NAME%" -InstallTestRunner -RunTests -Detailed
    echo.
) else (
    echo.
    echo ========================================
    echo Setup failed! Check the output above.
    echo ========================================
    echo.
)

pause
