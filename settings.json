{"al.enableCodeActions": false, "al.codeAnalyzers": ["${CodeCop}", "${PerTenantExtensionCop}", "${UICop}"], "al.ruleSetPath": "./custom.ruleset.json", "al.enableCodeAnalysis": true, "al.backgroundCodeAnalysis": "Project", "al.incrementalBuild": true, "al.packageCachePath": "./.alpackages", "al.browser": "Edge", "al.showMyCode": true, "al.enableSymbolLoadingAtStartup": true, "al.compilationOptions": {"parallel": true, "maxDegreeOfParallelism": 32}, "docker": {"containerName": "maxwell-bc-container", "imageName": "mcr.microsoft.com/businesscentral/onprem:26.0", "isolation": "hyperv", "memory": "8G", "sqlTimeout": 300, "useSSL": false, "enableTaskScheduler": false, "includeTestToolkit": true, "includeTestFrameworkOnly": false, "includeTestLibrariesOnly": false, "includePerformanceToolkit": false, "doNotExportObjectsToText": true, "shortcuts": "None", "enableSymbolLoading": true, "includeCSIDE": false, "multitenant": false, "assignPremiumPlan": false, "licenseFile": "", "databaseServer": "localhost", "databaseInstance": "", "databaseName": "maxwell-bc-db", "bakFile": "", "username": "admin", "password": "P@ssw0rd123!", "auth": "UserPassword", "timeZoneId": "Turkey Standard Time"}, "dependencies": {"appDependencies": [{"id": "92ab7586-aacb-4e43-a2f8-d4da27789098", "name": "Quality Control Management", "publisher": "Infotek Yazilim ve Donanim A.S.", "version": "********", "githubRepo": "https://github.com/Maxwell-Food/Quality-Control-Management", "downloadUrl": "", "localPath": "./.dependencies/QualityControlManagement"}, {"id": "8ea23f33-25c7-4044-9ddf-60ae33c232fa", "name": "G/L Acc. Names in Additional Languages by İnfotek", "publisher": "Infotek Yazilim ve Donanim A.S.", "version": "*********", "githubRepo": "https://github.com/Maxwell-Food/GL-Account-Names-Additional-Languages", "downloadUrl": "", "localPath": "./.dependencies/GLAccountNames"}]}, "buildPipeline": {"enableContinuousIntegration": true, "outputPath": "./output", "artifactName": "Maxwell-Customizations", "signApp": false, "validateApp": true, "runTests": true, "generateRuntimePackage": true}}