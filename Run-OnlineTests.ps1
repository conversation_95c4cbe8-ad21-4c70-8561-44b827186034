# Business Central Online Sandbox Test Runner
# Based on <PERSON>'s blog post: https://freddysblog.com/2021/02/15/running-tests-in-business-central-online-sandbox-environments/

param(
    [Parameter(Mandatory=$true)]
    [string]$Environment,

    [Parameter(Mandatory=$false)]
    [string]$TenantId,

    [Parameter(Mandatory=$false)]
    [string]$RefreshToken,

    [Parameter(Mandatory=$false)]
    [string]$ContainerName = "bcserver-filesonly",

    [Parameter(Mandatory=$false)]
    [switch]$InstallTestRunner,

    [Parameter(Mandatory=$false)]
    [switch]$PublishTestApp,

    [Parameter(Mandatory=$false)]
    [string]$TestAppPath,

    [Parameter(Mandatory=$false)]
    [switch]$RunTests,

    [Parameter(Mandatory=$false)]
    [string]$TestExtensionId,

    [Parameter(Mandatory=$false)]
    [switch]$Detailed
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $(
        switch($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            "INFO" { "Cyan" }
            default { "White" }
        }
    )
}

function Initialize-BcContainerHelper {
    Write-Log "Initializing BcContainerHelper..."

    try {
        # Check if BcContainerHelper is installed
        $module = Get-Module -ListAvailable -Name BcContainerHelper
        if (-not $module) {
            Write-Log "Installing BcContainerHelper module..."
            Install-Module -Name BcContainerHelper -Force -AllowClobber
        }

        # Import the module
        Import-Module BcContainerHelper -Force
        Write-Log "BcContainerHelper initialized successfully" -Level "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to initialize BcContainerHelper: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function New-AuthContext {
    param(
        [string]$TenantId,
        [string]$RefreshToken
    )

    Write-Log "Creating Business Central authentication context..."

    try {
        if ($RefreshToken) {
            Write-Log "Using provided refresh token"
            $authContext = New-BcAuthContext -refreshToken $RefreshToken
        } else {
            Write-Log "Interactive authentication required. Please sign in when prompted."
            if ($TenantId) {
                $authContext = New-BcAuthContext -includeDeviceLogin -tenantId $TenantId
            } else {
                $authContext = New-BcAuthContext -includeDeviceLogin
            }
        }

        if ($authContext) {
            Write-Log "Authentication context created successfully" -Level "SUCCESS"
            return $authContext
        } else {
            throw "Failed to create authentication context"
        }
    }
    catch {
        Write-Log "Authentication failed: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function New-FilesOnlyContainer {
    param([string]$ContainerName)

    Write-Log "Creating FilesOnly container for proxy operations..."

    try {
        # Check if container already exists
        $existingContainer = Get-BcContainers | Where-Object { $_.ContainerName -eq $ContainerName }
        if ($existingContainer) {
            Write-Log "Container $ContainerName already exists" -Level "INFO"
            return $true
        }

        # Create a FilesOnly container (minimal, fast)
        New-BcContainer `
            -accept_eula `
            -containerName $ContainerName `
            -artifactUrl (Get-BcArtifactUrl -type OnPrem -version "latest") `
            -filesOnly

        Write-Log "FilesOnly container created successfully" -Level "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to create FilesOnly container: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Install-TestRunnerFromAppSource {
    param(
        [object]$BcAuthContext,
        [string]$Environment,
        [string]$ContainerName
    )

    Write-Log "Installing Test Runner app from AppSource..."

    try {
        # Test Runner App ID from Freddy's blog post
        $testRunnerAppId = '23de40a6-dfe8-4f80-80db-d70f83ce8caf'

        Install-BcAppFromAppSource `
            -containerName $ContainerName `
            -bcAuthContext $BcAuthContext `
            -environment $Environment `
            -appId $testRunnerAppId `
            -appName 'Test Runner'

        Write-Log "Test Runner app installed successfully" -Level "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to install Test Runner app: $($_.Exception.Message)" -Level "ERROR"
        Write-Log "You may need to install it manually from Extension Marketplace" -Level "WARN"
        return $false
    }
}

function Install-TestFrameworkOnly {
    param(
        [object]$BcAuthContext,
        [string]$Environment,
        [string]$ContainerName
    )

    Write-Log "Installing Test Framework (Test Runner only)..."

    try {
        Import-TestToolkitToBcContainer `
            -containerName $ContainerName `
            -bcAuthContext $BcAuthContext `
            -environment $Environment `
            -includeTestRunnerOnly

        Write-Log "Test Framework installed successfully" -Level "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to install Test Framework: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Publish-TestAppToOnline {
    param(
        [object]$BcAuthContext,
        [string]$Environment,
        [string]$AppPath
    )

    Write-Log "Publishing test app to online environment..."

    try {
        if (-not (Test-Path $AppPath)) {
            throw "Test app file not found: $AppPath"
        }

        Publish-BcContainerApp `
            -bcAuthContext $BcAuthContext `
            -environment $Environment `
            -appFile $AppPath

        Write-Log "Test app published successfully" -Level "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to publish test app: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

function Get-TestExtensionId {
    param(
        [object]$BcAuthContext,
        [string]$Environment,
        [string]$ExtensionName
    )

    Write-Log "Getting extension ID for: $ExtensionName"

    try {
        $extensions = Get-BcInstalledExtensions -bcAuthContext $BcAuthContext -environment $Environment
        $testExtension = $extensions | Where-Object { $_.displayname -eq $ExtensionName }

        if ($testExtension) {
            Write-Log "Found extension ID: $($testExtension.id)" -Level "SUCCESS"
            return $testExtension.id
        } else {
            Write-Log "Extension not found: $ExtensionName" -Level "ERROR"
            Write-Log "Available extensions:" -Level "INFO"
            $extensions | ForEach-Object { Write-Log "  - $($_.displayname)" -Level "INFO" }
            return $null
        }
    }
    catch {
        Write-Log "Failed to get extension ID: $($_.Exception.Message)" -Level "ERROR"
        return $null
    }
}

function Invoke-OnlineTests {
    param(
        [object]$BcAuthContext,
        [string]$Environment,
        [string]$ContainerName,
        [string]$ExtensionId,
        [bool]$Detailed
    )

    Write-Log "Running tests in Business Central Online Sandbox..."

    try {
        $params = @{
            containerName = $ContainerName
            bcAuthContext = $BcAuthContext
            environment = $Environment
            extensionId = $ExtensionId
        }

        if ($Detailed) {
            $params.detailed = $true
        }

        Run-TestsInBcContainer @params

        Write-Log "Tests completed successfully" -Level "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Test execution failed: $($_.Exception.Message)" -Level "ERROR"
        return $false
    }
}

# ===== MAIN EXECUTION =====

function Main {
    try {
        Write-Log "Business Central Online Sandbox Test Runner"
        Write-Log "============================================="
        Write-Log "Environment: $Environment"
        Write-Log "Container: $ContainerName"

        # Step 1: Initialize BcContainerHelper
        $bcHelperReady = Initialize-BcContainerHelper
        if (-not $bcHelperReady) {
            Write-Log "Cannot proceed without BcContainerHelper" -Level "ERROR"
            exit 1
        }

        # Step 2: Create authentication context
        $authContext = New-AuthContext -TenantId $TenantId -RefreshToken $RefreshToken
        if (-not $authContext) {
            Write-Log "Cannot proceed without authentication context" -Level "ERROR"
            exit 1
        }

        # Step 3: Create FilesOnly container (required for proxy operations)
        $containerReady = New-FilesOnlyContainer -ContainerName $ContainerName
        if (-not $containerReady) {
            Write-Log "Cannot proceed without proxy container" -Level "ERROR"
            exit 1
        }

        # Step 4: Install Test Runner if requested
        if ($InstallTestRunner) {
            Write-Log "Installing Test Runner app..."
            $testRunnerInstalled = Install-TestRunnerFromAppSource -BcAuthContext $authContext -Environment $Environment -ContainerName $ContainerName

            if (-not $testRunnerInstalled) {
                Write-Log "Trying alternative Test Framework installation..." -Level "WARN"
                Install-TestFrameworkOnly -BcAuthContext $authContext -Environment $Environment -ContainerName $ContainerName
            }
        }

        # Step 5: Publish test app if requested
        if ($PublishTestApp -and $TestAppPath) {
            $publishSuccess = Publish-TestAppToOnline -BcAuthContext $authContext -Environment $Environment -AppPath $TestAppPath
            if (-not $publishSuccess) {
                Write-Log "Failed to publish test app. Cannot run tests." -Level "ERROR"
                exit 1
            }
        }

        # Step 6: Run tests if requested
        if ($RunTests) {
            if (-not $TestExtensionId) {
                Write-Log "TestExtensionId is required to run tests" -Level "ERROR"
                Write-Log "Use Get-BcInstalledExtensions to find your test extension ID" -Level "INFO"

                # Try to find Maxwell test extension automatically
                $maxwellTestId = Get-TestExtensionId -BcAuthContext $authContext -Environment $Environment -ExtensionName "Maxwell Basic Functions Test"
                if ($maxwellTestId) {
                    $TestExtensionId = $maxwellTestId
                    Write-Log "Found Maxwell test extension ID: $TestExtensionId" -Level "SUCCESS"
                } else {
                    exit 1
                }
            }

            $testSuccess = Invoke-OnlineTests -BcAuthContext $authContext -Environment $Environment -ContainerName $ContainerName -ExtensionId $TestExtensionId -Detailed $Detailed
            if (-not $testSuccess) {
                Write-Log "Test execution failed" -Level "ERROR"
                exit 1
            }
        }

        Write-Log "All operations completed successfully!" -Level "SUCCESS"
        Write-Log ""
        Write-Log "Useful commands for manual operations:"
        Write-Log "# Get installed extensions:"
        Write-Log "Get-BcInstalledExtensions -bcAuthContext `$authContext -environment '$Environment'"
        Write-Log ""
        Write-Log "# Run tests manually:"
        Write-Log "Run-TestsInBcContainer -containerName '$ContainerName' -bcAuthContext `$authContext -environment '$Environment' -extensionId '<extension-id>' -detailed"

    }
    catch {
        Write-Log "Script execution failed: $($_.Exception.Message)" -Level "ERROR"
        Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level "ERROR"
        exit 1
    }
    finally {
        # Cleanup: Remove the FilesOnly container if we created it
        try {
            if (Get-BcContainers | Where-Object { $_.ContainerName -eq $ContainerName }) {
                Write-Log "Cleaning up container: $ContainerName"
                Remove-BcContainer -containerName $ContainerName
            }
        }
        catch {
            Write-Log "Warning: Failed to cleanup container: $($_.Exception.Message)" -Level "WARN"
        }
    }
}

# Execute main function
Main
