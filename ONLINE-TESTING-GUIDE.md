# Business Central Online Sandbox Testing Guide

This guide explains how to run automated tests in Business Central Online Sandbox environments using the tools provided in this repository.

## Overview

Based on [<PERSON>'s blog post](https://freddysblog.com/2021/02/15/running-tests-in-business-central-online-sandbox-environments/), this setup allows you to run AL tests in your Business Central Online Sandbox environment instead of just in Docker containers.

## Prerequisites

1. **Business Central Online Sandbox Environment**
   - Access to a Business Central Online Sandbox environment
   - Environment name (e.g., "Maxwell-Sandbox")
   - Admin permissions to install apps

2. **PowerShell 5.1 or later**
   - Windows PowerShell or PowerShell Core
   - Administrator privileges for module installation

3. **Microsoft Account**
   - Account with access to your Business Central tenant
   - Appropriate permissions for the sandbox environment

## Quick Start

### Step 1: Setup Environment

Run the setup script to install Bc<PERSON>ontainerHelper and configure authentication:

```powershell
.\Setup-Dependencies.ps1 -SetupOnlineTestEnvironment -BCEnvironmentName "Maxwell-Sandbox"
```

This will:
- Install/update Bc<PERSON>ontainerHelper PowerShell module
- Prompt you to authenticate with Microsoft
- Test connection to your BC Online environment
- Verify Test Runner availability

### Step 2: Run Tests

Use the dedicated test runner script:

```powershell
# Complete workflow: Install Test Runner, Publish App, and Run Tests
.\Run-OnlineTests.ps1 -Environment "Maxwell-Sandbox" -InstallTestRunner -PublishTestApp -TestAppPath ".\output\YourTestApp.app" -RunTests -Detailed

# Or run individual steps:

# Just install Test Runner
.\Run-OnlineTests.ps1 -Environment "Maxwell-Sandbox" -InstallTestRunner

# Just run tests (if everything is already set up)
.\Run-OnlineTests.ps1 -Environment "Maxwell-Sandbox" -RunTests -TestExtensionId "your-test-extension-id" -Detailed
```

## Detailed Usage

### Setup-Dependencies.ps1 Parameters

| Parameter | Description | Required |
|-----------|-------------|----------|
| `-SetupOnlineTestEnvironment` | Enable online test environment setup | No |
| `-BCEnvironmentName` | Name of your BC Online environment | Yes (with setup) |
| `-TenantId` | Azure AD Tenant ID (optional) | No |

### Run-OnlineTests.ps1 Parameters

| Parameter | Description | Required | Default |
|-----------|-------------|----------|---------|
| `-Environment` | BC Online environment name | Yes | - |
| `-TenantId` | Azure AD Tenant ID | No | - |
| `-RefreshToken` | Stored refresh token for auth | No | - |
| `-ContainerName` | Proxy container name | No | "bcserver-filesonly" |
| `-InstallTestRunner` | Install Test Runner app | No | false |
| `-PublishTestApp` | Publish test app to environment | No | false |
| `-TestAppPath` | Path to test app file | No | - |
| `-RunTests` | Execute tests | No | false |
| `-TestExtensionId` | ID of test extension to run | No | - |
| `-Detailed` | Show detailed test output | No | false |

## Step-by-Step Workflow

### 1. Initial Setup (One-time)

```powershell
# Install BcContainerHelper and setup authentication
.\Setup-Dependencies.ps1 -SetupOnlineTestEnvironment -BCEnvironmentName "Maxwell-Sandbox"
```

### 2. Install Test Runner (One-time per environment)

```powershell
# Install Test Runner app from AppSource
.\Run-OnlineTests.ps1 -Environment "Maxwell-Sandbox" -InstallTestRunner
```

### 3. Build and Publish Your Test App

```powershell
# First, build your test app (if not already done)
# This should create a .app file in the output folder

# Then publish it to the online environment
.\Run-OnlineTests.ps1 -Environment "Maxwell-Sandbox" -PublishTestApp -TestAppPath ".\output\MaxwellCustomizations_Test.app"
```

### 4. Run Tests

```powershell
# Run tests with automatic extension ID detection
.\Run-OnlineTests.ps1 -Environment "Maxwell-Sandbox" -RunTests -Detailed

# Or specify the extension ID manually
.\Run-OnlineTests.ps1 -Environment "Maxwell-Sandbox" -RunTests -TestExtensionId "your-extension-id" -Detailed
```

## Authentication Options

### Interactive Authentication (Recommended for first-time setup)
The script will prompt you to sign in through your browser. This is the most secure method.

### Refresh Token (For automation)
After initial authentication, you can store the refresh token for automated scenarios:

```powershell
# Get refresh token from previous authentication
$authContext = New-BcAuthContext -includeDeviceLogin
$refreshToken = $authContext.RefreshToken

# Use stored refresh token
.\Run-OnlineTests.ps1 -Environment "Maxwell-Sandbox" -RefreshToken $refreshToken -RunTests -Detailed
```

## Troubleshooting

### Common Issues

1. **"Test Runner app not found"**
   - Install manually from Extension Marketplace in BC
   - Or use `-InstallTestRunner` parameter

2. **"Failed to create authentication context"**
   - Check your Microsoft account permissions
   - Verify tenant ID if specified
   - Try interactive authentication first

3. **"Extension not found"**
   - Verify your test app is published to the environment
   - Check extension name matches exactly
   - Use `Get-BcInstalledExtensions` to list available extensions

4. **"Container creation failed"**
   - Ensure Docker is running (for FilesOnly container)
   - Check available disk space
   - Try different container name

### Manual Commands

If the automated scripts fail, you can run commands manually:

```powershell
# Import BcContainerHelper
Import-Module BcContainerHelper

# Create auth context
$authContext = New-BcAuthContext -includeDeviceLogin

# List installed extensions
Get-BcInstalledExtensions -bcAuthContext $authContext -environment "Maxwell-Sandbox"

# Run tests manually
Run-TestsInBcContainer -containerName "bcserver" -bcAuthContext $authContext -environment "Maxwell-Sandbox" -extensionId "your-extension-id" -detailed
```

## Limitations

As noted in Freddy's blog post, running tests in BC Online has limitations:

1. **Test Framework Apps**: Not all test framework apps are available in online environments
2. **Test Libraries**: Advanced test libraries may not be available
3. **Dependencies**: Your tests must work with the limited set of available test frameworks

This is why the Maxwell project includes a `SimpleAssertMXW` codeunit as a replacement for the standard Library Assert.

## Next Steps

1. Review your test codeunits to ensure they work with available frameworks
2. Consider creating additional test scenarios for your Maxwell customizations
3. Integrate online testing into your CI/CD pipeline using refresh tokens
4. Monitor test results and maintain test coverage

For more information, refer to [Freddy Kristiansen's original blog post](https://freddysblog.com/2021/02/15/running-tests-in-business-central-online-sandbox-environments/).
