codeunit 61003 "Simple Assert MXW"
{
    // Simple assertion helper to replace Library Assert for SaaS environments
    // where test libraries may not be available

    procedure AreEqual(Expected: Variant; Actual: Variant; Message: Text)
    begin
        if Format(Expected) <> Format(Actual) then
            Error('ASSERTION FAILED: %1\Expected: %2\Actual: %3', Message, Expected, Actual);
    end;

    procedure IsTrue(Condition: Boolean; Message: Text)
    begin
        if not Condition then
            Error('ASSERTION FAILED: %1\Expected: TRUE\Actual: FALSE', Message);
    end;

    procedure IsFalse(Condition: Boolean; Message: Text)
    begin
        if Condition then
            Error('ASSERTION FAILED: %1\Expected: FALSE\Actual: TRUE', Message);
    end;

    procedure IsNotNull(Value: Variant; Message: Text)
    begin
        if Format(Value) = '' then
            Error('ASSERTION FAILED: %1\Expected: NOT NULL\Actual: NULL', Message);
    end;

    procedure IsNull(Value: Variant; Message: Text)
    begin
        if Format(Value) <> '' then
            Error('ASSERTION FAILED: %1\Expected: NULL\Actual: %2', Message, Value);
    end;

    procedure Fail(Message: Text)
    begin
        Error('TEST FAILED: %1', Message);
    end;
}