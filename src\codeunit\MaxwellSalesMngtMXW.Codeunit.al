codeunit 60004 "Maxwell Sales Mngt. MXW"
{
    SingleInstance = true;

    // var
    //     OverrideQuantityHandled: Boolean;

    /// <summary>
    /// Assigns item tracking information from warehouse shipment line details to the corresponding sales line.
    /// </summary>
    /// <param name="WarehouseShipmentLine">The warehouse shipment line to process.</param>
    procedure AssignItemTrackingInformationFromWarehouseShipmentLine(WarehouseShipmentLine: Record "Warehouse Shipment Line")
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
        WarehouseShipmentLineDtlForCalc: Record "Whse. Shipment Line Dtl. MXW";
        TotalUnshippedQuantity: Decimal;
    begin
        WarehouseShipmentLineDtl.SetRange("Document No.", WarehouseShipmentLine."No.");
        WarehouseShipmentLineDtl.SetRange("Document Line No.", WarehouseShipmentLine."Line No.");
        WarehouseShipmentLineDtl.SetRange("Item Tracking Info Assignd MXW", false);
        if WarehouseShipmentLineDtl.FindSet(true) then
            repeat
                AssignItemTrackingInformationFromWarehouseShipmentLineDetail(WarehouseShipmentLineDtl);
                WarehouseShipmentLineDtl."Item Tracking Info Assignd MXW" := true;
                WarehouseShipmentLineDtl.Modify(true);
            until WarehouseShipmentLineDtl.Next() = 0;

        WarehouseShipmentLine."Item Tracking Info Assignd MXW" := true;

        // Calculate total unshipped package quantities (exclude already shipped packages)
        WarehouseShipmentLineDtlForCalc.SetRange("Document No.", WarehouseShipmentLine."No.");
        WarehouseShipmentLineDtlForCalc.SetRange("Document Line No.", WarehouseShipmentLine."Line No.");
        WarehouseShipmentLineDtlForCalc.SetRange(Shipped, false);
        WarehouseShipmentLineDtlForCalc.CalcSums(Quantity);
        TotalUnshippedQuantity := WarehouseShipmentLineDtlForCalc.Quantity;

        // Populate Quantity to Ship with total unshipped package quantities
        WarehouseShipmentLine.Validate("Qty. to Ship", TotalUnshippedQuantity);
        WarehouseShipmentLine.Modify(true);
    end;

    /// <summary>
    /// Assigns item tracking information for all warehouse shipment lines in the header.
    /// </summary>
    /// <param name="WarehouseShipmentHeader">The warehouse shipment header to process.</param>
    procedure AssignItemTrackingInformationFromWarehouseShipmentHeader(WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    var
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
        ItemTrackAssignedMsg: Label 'Item Tracking Information has been assigned.';
    begin
        WarehouseShipmentLine.SetRange("No.", WarehouseShipmentHeader."No.");
        WarehouseShipmentLine.SetRange("Item Tracking Info Assignd MXW", false);
        if WarehouseShipmentLine.FindSet(true) then
            repeat
                this.AssignItemTrackingInformationFromWarehouseShipmentLine(WarehouseShipmentLine);
                WarehouseShipmentLine."Item Tracking Info Assignd MXW" := true;
                WarehouseShipmentLine.Modify(true);
            until WarehouseShipmentLine.Next() = 0;
        Message(ItemTrackAssignedMsg);
    end;

    local procedure AssignItemTrackingInformationFromSalesLine(LotNo: Code[50]; PackageNo: Code[50]; var SalesLine: Record "Sales Line"; AvailabilityDate: Date; QtyToShipBase: Decimal; QtyToShip: Decimal)
    var
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        SalesLineReserve: Codeunit "Sales Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";
    begin
        SalesLineReserve.InitFromSalesLine(TempSourceTrackingSpecification, SalesLine);

        TempTrackingSpecification.Init();
        TempTrackingSpecification."Lot No." := LotNo;
        if PackageNo <> '' then
            TempTrackingSpecification."Package No." := PackageNo;
        // if ExpirationDate <> 0D then
        //     TempTrackingSpecification."Expiration Date" := ExpirationDate;

        TempTrackingSpecification.SetQuantities(QtyToShipBase,
                                                QtyToShip,
                                                QtyToShipBase,
                                                0,
                                                0,
                                                0,
                                                0);
        TempTrackingSpecification.Insert(false);

        //ItemTrackingLines.SetBlockCommit(true);
        //this.OverrideQuantityHandled := true;
        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, AvailabilityDate, TempTrackingSpecification);
        //this.OverrideQuantityHandled := false;
    end;

    /// <summary>
    /// Assigns item tracking information from a specific warehouse shipment line detail.
    /// </summary>
    /// <param name="WarehouseShipmentLineDtl">The warehouse shipment line detail to process.</param>
    procedure AssignItemTrackingInformationFromWarehouseShipmentLineDetail(WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW")
    var
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
    begin
        WarehouseShipmentLine.Get(WarehouseShipmentLineDtl."Document No.", WarehouseShipmentLineDtl."Document Line No.");

        if WarehouseShipmentLine."Source Type" = Database::"Sales Line" then begin
            SalesLine.Get(WarehouseShipmentLine."Source Subtype", WarehouseShipmentLine."Source No.", WarehouseShipmentLine."Source Line No.");
            SalesHeader.Get(SalesLine."Document Type", SalesLine."Document No.");

            this.AssignItemTrackingInformationFromSalesLine(
                WarehouseShipmentLineDtl."Lot No.",
                WarehouseShipmentLineDtl."Package No.",
                SalesLine,
                SalesHeader."Posting Date",
                WarehouseShipmentLineDtl.Quantity,
                WarehouseShipmentLineDtl.Quantity
            );
        end;
    end;

    /// <summary>
    /// Handles the OnAfterInsert event for Item Ledger Entry to update shipped status.
    /// </summary>
    /// <param name="Rec">The Item Ledger Entry record.</param>
    /// <param name="RunTrigger">Whether the trigger is running.</param>
    procedure HandleOnAfterInsertItemLedgerEntry(var Rec: Record "Item Ledger Entry"; RunTrigger: Boolean)
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
    begin
        if (Rec."Lot No." = '') then
            exit;

        // Update the "Shipped" field for matching warehouse shipment line details
        WarehouseShipmentLineDtl.SetRange("Item No.", Rec."Item No.");
        WarehouseShipmentLineDtl.SetRange("Lot No.", Rec."Lot No.");
        WarehouseShipmentLineDtl.SetRange("Package No.", Rec."Package No.");

        if not WarehouseShipmentLineDtl.IsEmpty() then
            WarehouseShipmentLineDtl.ModifyAll(Shipped, true, true);
    end;

    /// <summary>
    /// Processes barcode for warehouse shipment and creates shipment line details.
    /// </summary>
    /// <param name="WarehouseShipmentHeader">The warehouse shipment header to process.</param>
    procedure ProcessBarcodeForWarehouseShipment(var WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    var
        PackageNoInformation: Record "Package No. Information";
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
        BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
        PackageAlreadyReadErr: Label 'This package has already been read in this warehouse shipment, please try a different package.';
        PackageNotFoundErr: Label 'Package No.: %1 not found.', Comment = '%1="WarehouseShipmentHeader.Barcode Text MXW"';
        NoMatchingShipmentLineErr: Label 'No matching warehouse shipment line found for package %1 with item %2.', Comment = '%1=Package No., %2=Item No.';
        PackageAlreadyShippedErr: Label 'Package %1 has already been shipped.', Comment = '%1=Package No.';
    begin
        if WarehouseShipmentHeader."Barcode Text MXW" = '' then
            exit;

        // Find package information by package number
        PackageNoInformation.SetRange("Package No.", WarehouseShipmentHeader."Barcode Text MXW");
        if not PackageNoInformation.FindFirst() then
            Error(PackageNotFoundErr, WarehouseShipmentHeader."Barcode Text MXW");

        // Check if package is expired and get user confirmation
        BasicFuncs.ConfirmExpiredPackageUsage(PackageNoInformation."Item No.", PackageNoInformation."Variant Code", PackageNoInformation."Package No.");

        // Check if package has already been read in this shipment
        WarehouseShipmentLineDtl.SetRange("Document No.", WarehouseShipmentHeader."No.");
        WarehouseShipmentLineDtl.SetRange("Package No.", PackageNoInformation."Package No.");
        if not WarehouseShipmentLineDtl.IsEmpty() then
            Error(PackageAlreadyReadErr);

        // Find matching warehouse shipment line
        WarehouseShipmentLine.SetRange("No.", WarehouseShipmentHeader."No.");
        WarehouseShipmentLine.SetRange("Item No.", PackageNoInformation."Item No.");
        WarehouseShipmentLine.SetRange("Variant Code", PackageNoInformation."Variant Code");
        if not WarehouseShipmentLine.FindFirst() then
            Error(NoMatchingShipmentLineErr, PackageNoInformation."Package No.", PackageNoInformation."Item No.");

        // Check if package is already shipped
        WarehouseShipmentLineDtl.SetRange("Package No.", PackageNoInformation."Package No.");
        WarehouseShipmentLineDtl.SetRange(Shipped, true);
        if not WarehouseShipmentLineDtl.IsEmpty() then
            Error(PackageAlreadyShippedErr, PackageNoInformation."Package No.");

        // Create warehouse shipment line detail
        CreateWarehouseShipmentLineDetail(WarehouseShipmentLine, PackageNoInformation);

        // Clear barcode field for next scan
        WarehouseShipmentHeader."Barcode Text MXW" := '';
        WarehouseShipmentHeader.Modify(true);

        Message('Package %1 successfully added to warehouse shipment.', PackageNoInformation."Package No.");
    end;

    local procedure CreateWarehouseShipmentLineDetail(WarehouseShipmentLine: Record "Warehouse Shipment Line"; PackageNoInformation: Record "Package No. Information")
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
        Item: Record Item;
        SalesLine: Record "Sales Line";
        BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
    begin
        WarehouseShipmentLineDtl.Init();
        WarehouseShipmentLineDtl."Document No." := WarehouseShipmentLine."No.";
        WarehouseShipmentLineDtl."Document Line No." := WarehouseShipmentLine."Line No.";
        WarehouseShipmentLineDtl."Package No." := PackageNoInformation."Package No.";
        WarehouseShipmentLineDtl."Item No." := PackageNoInformation."Item No.";
        WarehouseShipmentLineDtl."Variant Code" := PackageNoInformation."Variant Code";
        WarehouseShipmentLineDtl.Quantity := PackageNoInformation.Inventory;
        WarehouseShipmentLineDtl."Unit of Measure Code" := WarehouseShipmentLine."Unit of Measure Code";
        WarehouseShipmentLineDtl.Shipped := false;

        // Get item description
        if Item.Get(PackageNoInformation."Item No.") then
            WarehouseShipmentLineDtl."Item Description" := BasicFuncs.GetItemDescription(WarehouseShipmentLineDtl."Item No.", WarehouseShipmentLineDtl."Variant Code");

        // Get lot number and expiration date from package information extension
        WarehouseShipmentLineDtl."Lot No." := PackageNoInformation."Lot No. MXW";
        WarehouseShipmentLineDtl."Expiration Date" := PackageNoInformation."Expiration Date MXW";

        // Try to get sales order number from warehouse shipment line source
        if WarehouseShipmentLine."Source Type" = Database::"Sales Line" then
            if SalesLine.Get(WarehouseShipmentLine."Source Subtype", WarehouseShipmentLine."Source No.", WarehouseShipmentLine."Source Line No.") then
                WarehouseShipmentLineDtl."Sales Order No." := SalesLine."Document No.";

        // Set item tracking info assigned flag
        WarehouseShipmentLineDtl."Item Tracking Info Assignd MXW" := (WarehouseShipmentLineDtl."Lot No." <> '') or (WarehouseShipmentLineDtl."Expiration Date" <> 0D);

        // Set package count and total package quantity
        WarehouseShipmentLineDtl."Package Count" := 1;
        WarehouseShipmentLineDtl."Total Package Qty." := PackageNoInformation.Inventory;

        WarehouseShipmentLineDtl.Insert(true);
    end;

    /// <summary>
    /// Gets the count of warehouse shipment line details for a specific document and line.
    /// </summary>
    /// <param name="DocumentNo">The document number.</param>
    /// <param name="DocumentLineNo">The document line number.</param>
    /// <returns>The count of warehouse shipment line details.</returns>
    procedure GetWarehouseShipmentLineDetails(DocumentNo: Code[20]; DocumentLineNo: Integer): Integer
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
    begin
        WarehouseShipmentLineDtl.SetRange("Document No.", DocumentNo);
        WarehouseShipmentLineDtl.SetRange("Document Line No.", DocumentLineNo);
        exit(WarehouseShipmentLineDtl.Count());
    end;

    /// <summary>
    /// Gets the total package quantity for a specific document and line.
    /// </summary>
    /// <param name="DocumentNo">The document number.</param>
    /// <param name="DocumentLineNo">The document line number.</param>
    /// <returns>The total package quantity.</returns>
    procedure GetTotalPackageQuantity(DocumentNo: Code[20]; DocumentLineNo: Integer): Decimal
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
        TotalQuantity: Decimal;
    begin
        WarehouseShipmentLineDtl.SetRange("Document No.", DocumentNo);
        WarehouseShipmentLineDtl.SetRange("Document Line No.", DocumentLineNo);
        if WarehouseShipmentLineDtl.FindSet() then
            repeat
                TotalQuantity += WarehouseShipmentLineDtl.Quantity;
            until WarehouseShipmentLineDtl.Next() = 0;
        exit(TotalQuantity);
    end;
}
