{"id": "1234567c-96b6-4d43-9ff2-b108a93fc5bb", "name": "Maxwell Customizations Tests", "publisher": "Infotek Yazilim ve Donanim A.S.", "version": "********", "brief": "Test suite for Maxwell Customizations", "description": "Automated tests for Maxwell Customizations Business Central extension", "privacyStatement": "", "EULA": "", "help": "", "url": "", "logo": "", "dependencies": [{"id": "8326694c-96b6-4d43-9ff2-b108a93fc5bb", "name": "Maxwell Customizations", "publisher": "Infotek Yazilim ve Donanim A.S.", "version": "********"}, {"id": "dd0be2ea-f733-4d65-bb34-a28f4624fb14", "name": "Library Assert", "publisher": "Microsoft", "version": "********"}, {"id": "5d86850b-0d76-4eca-bd7b-951ad998e997", "name": "Tests-TestLibraries", "publisher": "Microsoft", "version": "********"}], "screenshots": [], "platform": "*******", "application": "********", "idRanges": [{"from": 61000, "to": 61999}], "resourceExposurePolicy": {"allowDebugging": true, "allowDownloadingSource": true, "includeSourceInSymbolFile": true}, "runtime": "15.2", "features": ["NoImplicitWith", "TranslationFile"]}