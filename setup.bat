@echo off
echo Maxwell Customizations - Quick Setup
echo =====================================
echo.

echo Setting up dependencies...
powershell -ExecutionPolicy Bypass -File "Setup-Dependencies.ps1"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Dependencies setup completed successfully!
    echo.
    echo Next steps:
    echo 1. Open VS Code in this folder
    echo 2. Run 'AL: Download Symbols' command
    echo 3. Use 'PublishAppToDocker.ps1' to publish to Docker
    echo.
    pause
) else (
    echo.
    echo Dependencies setup failed! Check the output above for errors.
    echo.
    pause
)