# Getting Started with Testing - Maxwell Customizations\n\nThis document provides a quick guide to get started with automated testing for the Maxwell Customizations project.\n\n## What's Been Set Up\n\nA basic test infrastructure has been created in the `/test` directory with:\n\n- **Test Project Structure**: Separate test project with proper dependencies\n- **Sample Test Codeunit**: Tests for `Maxwell Basic Functions MXW` codeunit\n- **Test Runner**: Automated test execution\n- **Documentation**: Complete setup and usage instructions\n\n## Quick Start Steps\n\n### 1. Prerequisites Check\n\nEnsure your Business Central environment has the Test Framework installed:\n\n```powershell\n# For Docker environments\nImport-TestToolkitToBCContainer -containerName <your-container-name>\n\n# OR during container creation\nNew-BCContainer -includeTestToolkit\n```\n\n### 2. Deploy the Test Project\n\n1. Open VS Code in the `/test` directory\n2. Run \"AL: Download Symbols\" to get dependencies\n3. Run \"AL: Package\" to build the test extension\n4. Deploy to your Maxwell sandbox environment\n\n### 3. Run Your First Tests\n\n1. Open Business Central\n2. Search for \"Test Tool\" page\n3. Run codeunit 61002 \"Maxwell Test Runner MXW\"\n4. View test results in the message window\n\n## What Tests Are Included\n\nThe initial test suite covers:\n\n- **Item Description Logic**: Tests `GetItemDescription()` with various scenarios\n- **Lot Expiration Checking**: Tests `IsLotExpired()` with expired/valid/empty lots  \n- **Package Expiration Checking**: Tests `IsPackageExpired()` with expired/valid/empty packages\n\n## Next Steps\n\n1. **Run the basic tests** to ensure everything works\n2. **Add tests for other codeunits** like Purchase Management, Sales Management, etc.\n3. **Implement UI handlers** for testing user confirmation dialogs\n4. **Add integration tests** for end-to-end workflow testing\n5. **Set up continuous testing** in your development process\n\n## Project Structure\n\n```\nMaxwellCustomizations/\n├── src/                    # Main extension code\n│   ├── codeunit/\n│   ├── table/\n│   └── ...\n├── test/                   # NEW: Test project\n│   ├── app.json           # Test dependencies\n│   ├── src/\n│   │   ├── MaxwellBasicFunctionsTest.Codeunit.al\n│   │   └── MaxwellTestRunnerMXW.Codeunit.al\n│   └── README.md          # Detailed test documentation\n└── TESTING-QUICKSTART.md  # This file\n```\n\n## Benefits of This Setup\n\n✅ **Automated Quality Assurance**: Catch bugs before they reach production\n✅ **Regression Prevention**: Ensure changes don't break existing functionality  \n✅ **Documentation**: Tests serve as executable documentation\n✅ **Confidence**: Safe refactoring and feature development\n✅ **Professional Standards**: Industry best practices for AL development\n\n## Support and Documentation\n\nFor detailed information, see:\n- `/test/README.md` - Complete test project documentation\n- Microsoft Learn - Business Central test automation guides\n- Maxwell development team for project-specific questions\n\n---\n\n**Ready to start testing?** Navigate to the `/test` directory and follow the setup instructions!