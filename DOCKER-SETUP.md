# Maxwell Customizations - Docker & CI/CD Setup

This document describes the Docker publishing and dependency management setup for Maxwell Customizations Business Central extension.

## Overview

The project now includes automated dependency management, Docker publishing capabilities, and CI/CD pipeline integration. All dependencies are automatically downloaded from their GitHub repositories and configured for local development.

## Files Added

### Configuration Files
- **`settings.json`** - Main configuration file containing Docker settings, dependency definitions, and build pipeline configuration
- **`docker-compose.yml`** - Docker Compose configuration for local development environment

### Scripts
- **`PublishAppToDocker.ps1`** - Main script for publishing the app to Docker containers
- **`Setup-Dependencies.ps1`** - Dependency management script for local development setup

### CI/CD
- **`.github/workflows/ci-cd.yml`** - GitHub Actions workflow for automated build, test, and deployment

## Quick Start

### 1. Local Development Setup

```powershell
# Install and setup all dependencies locally
.\Setup-Dependencies.ps1

# Validate dependencies (optional)
.\Setup-Dependencies.ps1 -ValidateOnly

# Update existing dependencies
.\Setup-Dependencies.ps1 -Update

# Clean and reinstall dependencies
.\Setup-Dependencies.ps1 -Clean
```

### 2. Docker Publishing

```powershell
# Publish to Docker with all dependencies
.\PublishAppToDocker.ps1

# Force recreate container
.\PublishAppToDocker.ps1 -Force

# Skip dependency download (if already available)
.\PublishAppToDocker.ps1 -SkipDependencies

# Use custom container name
.\PublishAppToDocker.ps1 -ContainerName "my-custom-container"
```

### 3. Docker Compose (Alternative)

```powershell
# Start Business Central container with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f

# Stop container
docker-compose down
```

## Configuration Details

### settings.json Structure

The `settings.json` file contains several configuration sections:

#### AL Configuration
- Code analysis settings
- Compilation options
- Package cache configuration

#### Docker Configuration
```json
"docker": {
  "containerName": "maxwell-bc-container",
  "imageName": "mcr.microsoft.com/businesscentral/onprem:26.0",
  "memory": "8G",
  "username": "admin",
  "password": "P@ssw0rd123!",
  "auth": "UserPassword"
}
```

#### Dependencies Configuration
```json
"dependencies": {
  "appDependencies": [
    {
      "id": "92ab7586-aacb-4e43-a2f8-d4da27789098",
      "name": "Quality Control Management",
      "githubRepo": "https://github.com/Maxwell-Food/Quality-Control-Management",
      "localPath": "./.dependencies/QualityControlManagement"
    }
  ]
}
```

## Dependencies

The project has two main dependencies that are automatically managed:

### 1. Quality Control Management
- **ID**: 92ab7586-aacb-4e43-a2f8-d4da27789098
- **Version**: ********
- **GitHub**: https://github.com/Maxwell-Food/Quality-Control-Management

### 2. G/L Account Names in Additional Languages
- **ID**: 8ea23f33-25c7-4044-9ddf-60ae33c232fa
- **Version**: *********
- **GitHub**: https://github.com/Maxwell-Food/GL-Account-Names-Additional-Languages

## CI/CD Pipeline

The GitHub Actions workflow (`.github/workflows/ci-cd.yml`) automatically:

1. **Build Phase**:
   - Downloads and sets up dependencies
   - Creates Business Central container
   - Compiles the AL application
   - Runs tests (if available)
   - Creates build artifacts

2. **Dependency Update Phase**:
   - Updates GitHub repository links in `settings.json`
   - Commits changes back to the repository

3. **Docker Publish Phase**:
   - Publishes the app to a production Docker container
   - Creates deployment summary

### Triggering the Pipeline

The pipeline runs automatically on:
- Push to `main` or `develop` branches
- Pull requests to `main` branch
- Manual trigger via GitHub Actions UI

## Environment Access

After successful Docker deployment, access the environment at:

- **Web Client**: http://localhost:8080/BC/
- **Username**: admin
- **Password**: P@ssw0rd123! (configurable in settings.json)

## Directory Structure

```
MaxwellCustomizations/
├── .dependencies/              # Auto-downloaded dependencies
│   ├── QualityControlManagement/
│   └── GLAccountNames/
├── .github/
│   └── workflows/
│       └── ci-cd.yml          # CI/CD pipeline
├── output/                    # Compiled app files
├── src/                       # Source code
├── settings.json              # Main configuration
├── docker-compose.yml         # Docker Compose setup
├── PublishAppToDocker.ps1     # Docker publishing script
└── Setup-Dependencies.ps1     # Dependency management script
```

## Troubleshooting

### Common Issues

1. **Docker Container Creation Fails**
   - Ensure Hyper-V is enabled on Windows
   - Check available memory (requires 8GB+)
   - Verify Docker is running and configured for Windows containers

2. **Dependency Download Fails**
   - Check internet connectivity
   - Verify GitHub repository URLs in settings.json
   - Ensure Git is installed and accessible

3. **App Compilation Fails**
   - Run `AL: Download Symbols` in VS Code
   - Check dependency versions in app.json vs settings.json
   - Validate dependency apps are properly installed

### Useful Commands

```powershell
# Check container status
Get-BcContainers

# Remove all BC containers
Get-BcContainers | Remove-BcContainer -force

# Download BC symbols in VS Code
# Ctrl+Shift+P -> "AL: Download Symbols"

# Manual app compilation
Compile-AppInBcContainer -containerName "maxwell-bc-container" -appProjectFolder "."
```

## Security Notes

- Default credentials are configured for development only
- Production deployments should use secure credentials
- License files should be provided for production environments
- Consider using Azure Key Vault for production secrets

## Support

For issues related to:
- **Maxwell Customizations**: Contact the development team
- **Business Central containers**: Refer to Microsoft documentation
- **GitHub Actions**: Check the Actions tab in the repository