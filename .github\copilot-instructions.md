# GitHub Copilot Instructions for Maxwell Customizations (Business Central AL)

"instructions

You are an AI assistant specialized in AL for the Maxwell per-tenant extension (PTE) targeting Business Central 26.0. Keep changes small, follow existing patterns, and reference the files below when in doubt.

# GitHub Copilot Instructions for Maxwell Customizations (Business Central AL)

## Architecture and Business Domains

- **Core domains**: packaging workflow, warehouse management, purchase processing, sales operations, production trackingYou are an AI assistant specialized in AL for the Maxwell per-tenant extension (PTE) targeting Business Central 26.0. Keep changes small, follow existing patterns, and reference the files below when in doubt.

- **Package-centric model**: Most workflows revolve around package creation, tracking, and transfer. See `PackageTransferOrderMXW.Page.al` and related tables

- **Quality control integration**: Depends on Quality Control Management extension; validate QC status before package operations (see `MaxwellPurchaseMngtMXW.Codeunit.al` line 15-18)## Architecture and repo map

- **Barcode-driven operations**: Many processes use barcode scanning (e.g., `WarehouseShipmentHeader."Barcode Text MXW"` in warehouse shipment processing)- Source lives in `src/` with: `codeunit/`, `page/`, `pageextension/`, `report/`, `reportlayout/`, `table/`, `tableextension/`, `enum/`.

- Key domains: purchasing, production, sales, warehouse, and packaging. Core orchestrators are codeunits like `MaxwellPurchaseMngtMXW.Codeunit.al`, `MaxwellProductionMngtMXW.Codeunit.al`, `MaxwellSalesMngtMXW.Codeunit.al`, and `MaxwellWhseShipmentMgtMXW.Codeunit.al`.

## Key Management Codeunits (Business Logic Layer)- UI integrates via page/pageextension (e.g., `PackageTransferOrderMXW.Page.al`, `WhseShipmentLineDtlMXW.Page.al`). Reports under `src/report` with RDLC in `src/reportlayout`.

- `MaxwellPurchaseMngtMXW` (60001): Package creation from warehouse receipts, lot tracking validation- Object ID range: 60000–60999 (see `app.json`). New objects must stay in-range.

- `MaxwellWhseShipmentMgtMXW` (60008): Barcode processing, package shipment validation

- `MaxwellBasicFunctionsMXW` (60010): Shared utilities (user lookups, item descriptions, expiration checks)## Platform, dependencies, and features

- `MaxwellPackageTransMgtMXW`: Package transfer order management- app.json: application ********, runtime 15.2, publisher "Infotek Yazilim ve Donanim A.S.".

- Pattern: SingleInstance codeunits for stateful operations, procedures named by business action- Dependencies: Quality Control Management (********) and G/L Acc. Names in Additional Languages (*********). Code may interact with QC records and GL names.

- Features: `NoImplicitWith` (always qualify record variables) and `TranslationFile` (resources in XLF). Resource exposure allows debugging and downloading source.

## Data Model Patterns

- **Table extensions**: Add MXW-suffixed fields to standard BC tables (see `WarehouseReceiptLineMXW.TableExt.al`)## Build, debug, translations

- **FlowFields for aggregation**: Use `CalcFormula` for counts/sums (e.g., "Package Count MXW", "Total Package Quantity MXW")- Build/package via VS Code: "AL: Package". A workspace task labeled "Build" is available and invokes packaging. Always run "AL: Download Symbols" if types are missing.

- **Validation triggers**: Implement business rules in table field OnValidate (e.g., lot number change prevention when packages exist)- Target sandbox/environment details are maintained in `launch.json` (see README for typical "Maxwell-Sandbox"). Verify before publishing/debugging.

- **Enums for choices**: Simple extensible enums like `PackageCreationMethodMXW` (Single/Multiple)- Translations are in `Translations/Maxwell Customizations.g.xlf`. Keep XLF entries in sync with captions/tooltips; use features TranslationFile and follow existing patterns.



## UI Extension Patterns## Conventions and patterns used here

- **Page extensions**: Add fields using `addafter()` positioning, group related MXW fields together- Object naming: PascalCase with the suffix `MXW` (e.g., `MaxwellSalesMngtMXW`). Keep under 30 chars; be descriptive.

- **Action integration**: Add barcode/package actions to `F&unctions` group with proper icons (LotInfo, Package, etc.)- Variable declaration order: Record → Report → Codeunit → XmlPort → Page → Query → Notification → system types → simple types → complex types → collections.

- **Detail pages**: Create dedicated detail pages for complex data entry (WhseReceiptLineDetailsMXW, WhseShipmentLineDtlMXW)- Always use parentheses for procedure calls (e.g., `MyProc()`), and explicit parameters for record ops (e.g., `Insert(false)`).

- **ApplicationArea = All**: Use at object level, avoid field-level ApplicationAreaAlways use parentheses for property and method access, even for property-like access (per LC0077). For example, use `RecRef.Number()` not `RecRef.Number`.

	- LinterCop LC0077: Parentheses are required for all property/method access in AL. Example:

## Object Naming and ID Management		- Good: `RecRef.Number()`

- **Suffix convention**: All custom objects end with `MXW` (Maxwell)		- Avoid: `RecRef.Number`

- **ID range**: 60000-60999 strictly enforced (see app.json idRanges)- Event subscribers: use proper signatures (no quoted event names). Prefer publishers in codeunits; avoid business logic in pages.

- **Field IDs**: Start table extension fields at 60000, increment sequentially	- LinterCop LC0028: EventSubscriber arguments must use identifier syntax instead of string literals. For example:

- **Descriptive names**: `MaxwellWhseShipmentMgtMXW` not `WhseShipMgtMXW`		- Good: `[EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", OnBeforeRunWithCheck, '', false, false)]`

		- Avoid: `[EventSubscriber(ObjectType::Codeunit, Codeunit::"Gen. Jnl.-Post Line", 'OnBeforeRunWithCheck', '', false, false)]`

## Critical Development Patterns- JSON: use AL JSON types and direct getters (e.g., `GetText()`). TempBlob: use streams from `CreateInStream()`/`CreateOutStream()` directly.

- **Always qualify records**: `NoImplicitWith` feature requires `Rec.FieldName` not `FieldName`- Multiline text: use `@'...'`. Conditionals: split `else if` (as separate `else` then `if`) or prefer `case true of` for multiple branches.

- **Parentheses mandatory**: Use `RecRef.Number()` not `RecRef.Number` (LC0077)- UI ApplicationArea at object level; tooltips defined on table fields rather than page controls.

- **Event subscriber syntax**: Use identifiers not strings: `OnBeforeRunWithCheck` not `'OnBeforeRunWithCheck'` (LC0028)

- **Error handling**: Use descriptive labels with placeholders: `Label 'Package %1 not found.', Comment = '%1=Package No.'`## Cross-component flow and examples

- Number series are configured in setup and assigned via codeunits; mirror the patterns in `MaxwellPurchaseMngtMXW.Codeunit.al`.

## Dependencies and Integration Points- Packaging/warehouse flows are surfaced through pages like `PackageTransferOrderMXW.Page.al`, `WhseReceiptLineDetailsMXW.Page.al`, and `WhseShipmentLineDtlMXW.Page.al`.

- **Quality Control Management (********)**: Status enums, document validation before package creation- Setup/config: see `src/page/MaxwellSetupMXW.Page.al` and `src/table/MaxwellSetupMXW.Table.al` for how configuration is stored and accessed.

- **G/L Account Names Extension (*********)**: Multi-language GL account handling

- **Standard BC integration**: Extend warehouse, purchase, sales tables; hook into posting events## Analyzers and rules

- CodeCop, PerTenantExtensionCop, and UICop are enabled. AppSourceCop is intentionally disabled for this PTE.

## Build and Debug Workflow- Project rules live in `custom.ruleset.json` (based on Stefan Maron’s PTE rules). Fix warnings that matter to this project; keep style consistent.

- **Build**: VS Code "AL: Package" or workspace "Build" task

- **Symbol download**: Always run "AL: Download Symbols" when missing types## Path formatting note

- **Translations**: Sync captions/tooltips with `Translations/Maxwell Customizations.g.xlf`- When invoking the filesystem MCP tools on Windows, prefer the canonical Windows-style path (for example: `C:\Users\<USER>\Dropbox\AL\MaxwellCustomizations\`).

- **Rules**: Follow `custom.ruleset.json` (Stefan Maron PTE rules with project-specific disabled rules)- I initially hit an "access denied" error when using a lowercase or differently formatted path; switching to the exact backslash-correct, canonical path fixed the issue. This avoids accidental mismatches in allowed-directory checks.



## Setup and Configuration"instructions

- **Central setup**: `MaxwellSetupMXW` table (60000) with number series, journal templates
- **Number series pattern**: Configure in setup, assign in management codeunits (see Purchase Mngt pattern)
- **Location-based config**: Most warehouse operations require location setup integration

## File Organization
```
src/
├── codeunit/          # Business logic, SingleInstance management units
├── table/             # New entities (Package Transfer Header/Line, etc.)
├── tableextension/    # Extensions to standard BC tables with MXW fields
├── page/              # New UI (Package Transfer Order, Creation wizards)
├── pageextension/     # UI extensions (add MXW fields to standard pages)
├── enum/              # Simple choice lists (PackageCreationMethodMXW)
├── report/            # RDLC reports with layouts in reportlayout/
└── permissionset/     # Security (MaxwellPermMXW)
```

## Common Anti-patterns to Avoid
- Don't put business logic in page triggers - use codeunit procedures
- Don't hardcode IDs - always check they're in 60000-60999 range
- Don't forget to validate expiration dates for lot-tracked items
- Don't create packages without checking Quality Control status first

I discovered that to add a due date when creating a Jira issue using the MCP server:

Field Name: duedate (in the additional_fields parameter)
Format: YYYY-MM-DD (ISO date format)
Implementation: Added via the additional_fields parameter in the createJiraIssue function
Example: {"duedate": "2025-08-30"}
The due date parameter is successfully integrated and the issue has been created with all the required information relevant to the Maxwell Business Central customizations project.
