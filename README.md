# Maxwell Customizations - Test Project

This is a basic test suite for the Maxwell Customizations Business Central extension.

## Prerequisites

Before running these tests, you need to ensure your Business Central environment has the following installed:

1. **Test Framework** - The core Business Central test framework
2. **Library Assert** - Provides assertion methods for tests  
3. **Tests-TestLibraries** - Additional test utilities

### Installing Test Framework

The test framework needs to be installed in your Business Central environment. There are several ways to do this:

#### Option 1: Using BCContainerHelper (Recommended for Docker environments)
```powershell
Import-TestToolkitToBCContainer -containerName <your-container-name>
```

#### Option 2: During Container Creation
```powershell
New-BCContainer -includeTestToolkit
```

#### Option 3: Framework Only (Minimal installation)
```powershell
New-BCContainer -includeTestFrameworkOnly
```

## Project Structure

```
test/
├── app.json                              # Test project manifest with dependencies
├── src/
│   ├── MaxwellBasicFunctionsTest.Codeunit.al    # Tests for basic functions
│   └── MaxwellTestRunnerMXW.Codeunit.al         # Test runner to execute all tests
└── README.md                             # This file
```

## Running Tests

### Option 1: Using Test Runner Codeunit
1. Open Business Central
2. Search for "Test Tool" page
3. Run codeunit 61002 "Maxwell Test Runner MXW"

### Option 2: Individual Test Codeunit
1. Open Business Central  
2. Search for "Test Tool" page
3. Run codeunit 61001 "Maxwell Basic Functions Test"

### Option 3: Command Line (Advanced)
You can also run tests programmatically using PowerShell with BCContainerHelper.

## Test Coverage

Currently includes basic tests for:

- ✅ `GetItemDescription()` - Tests item and variant description retrieval
- ✅ `IsLotExpired()` - Tests lot expiration checking logic
- ✅ `IsPackageExpired()` - Tests package expiration checking logic
- ❌ `ConfirmExpiredLotUsage()` - Requires UI handler (advanced)
- ❌ `ConfirmExpiredPackageUsage()` - Requires UI handler (advanced)
- ❌ `GetUserFullNameFromSecurityId()` - Not yet implemented
- ❌ `GetUserNameFromSecurityId()` - Not yet implemented

## Adding New Tests

To add new tests:

1. Create new test methods in existing test codeunits OR create new test codeunits
2. Decorate methods with `[Test]` attribute
3. Use `LibraryAssert` for assertions:
   - `LibraryAssert.AreEqual(expected, actual, message)`
   - `LibraryAssert.IsTrue(condition, message)`  
   - `LibraryAssert.IsFalse(condition, message)`
4. Follow naming convention: `TestMethodName_WithScenario()`
5. Use Given/When/Then comments for clarity
6. Clean up test data in each test method

## Dependencies in app.json

The test project depends on:
- Main Maxwell Customizations extension (ID: 8326694c-96b6-4d43-9ff2-b108a93fc5bb)
- Library Assert (Microsoft) 
- Tests-TestLibraries (Microsoft)

## Object ID Range

Test objects use ID range 61000-61999 to avoid conflicts with the main extension (60000-60999).

## Best Practices

1. **Keep tests fast** - Each test should run in seconds, not minutes
2. **Test isolation** - Each test should be independent and clean up after itself  
3. **Use random data** - Avoid hardcoded values where possible
4. **Clear naming** - Test method names should clearly describe what is being tested
5. **Single responsibility** - Each test method should test one specific scenario
6. **Handle errors gracefully** - Use proper error handling and assertions

## Troubleshooting

### Common Issues

1. **"Library Assert not found"** - Test Framework not installed in environment
2. **"Dependency not found"** - Main extension not installed or wrong version
3. **Object ID conflicts** - Check ID ranges in app.json
4. **Permission errors** - Ensure proper test permissions are assigned

### Support

For issues specific to Maxwell Customizations tests, check:
1. Main extension is installed and up to date
2. All dependencies are properly referenced in app.json
3. Test Framework is installed in your environment
4. Object ID ranges don't conflict