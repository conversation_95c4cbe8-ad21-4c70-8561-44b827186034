name: Maxwell Customizations CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  APP_NAME: "Maxwell Customizations"
  PUBLISHER: "Infotek Yazilim ve Donanim A.S."
  BC_VERSION: "26.0"
  CONTAINER_NAME: "maxwell-bc-ci"

jobs:
  build-and-test:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup PowerShell modules
      shell: powershell
      run: |
        Set-PSRepository PSGallery -InstallationPolicy Trusted
        Install-Module BcContainerHelper -Force
        Import-Module BcContainerHelper -Force

    - name: Read app.json
      id: app-info
      shell: powershell
      run: |
        $appInfo = Get-Content "app.json" -Raw | ConvertFrom-Json
        echo "APP_VERSION=$($appInfo.version)" >> $env:GITHUB_ENV
        echo "APP_ID=$($appInfo.id)" >> $env:GITHUB_ENV
        echo "App version: $($appInfo.version)"

    - name: Setup dependencies directory
      shell: powershell
      run: |
        New-Item -ItemType Directory -Path ".dependencies" -Force

    - name: Download Quality Control Management dependency
      shell: powershell
      run: |
        $repoUrl = "https://github.com/Maxwell-Food/Quality-Control-Management"
        $localPath = ".dependencies/QualityControlManagement"
        
        # Try to clone the repository
        try {
          git clone $repoUrl $localPath
          Write-Host "Successfully cloned Quality Control Management dependency"
        } catch {
          Write-Host "Failed to clone dependency repository. Creating placeholder structure."
          New-Item -ItemType Directory -Path $localPath -Force
          # Create a placeholder app.json for dependency resolution
          $placeholderApp = @{
            id = "92ab7586-aacb-4e43-a2f8-d4da27789098"
            name = "Quality Control Management"
            publisher = "Infotek Yazilim ve Donanim A.S."
            version = "********"
          }
          $placeholderApp | ConvertTo-Json -Depth 3 | Out-File -FilePath "$localPath/app.json" -Encoding UTF8
        }

    - name: Download GL Account Names dependency
      shell: powershell
      run: |
        $repoUrl = "https://github.com/Maxwell-Food/GL-Account-Names-Additional-Languages"
        $localPath = ".dependencies/GLAccountNames"
        
        # Try to clone the repository
        try {
          git clone $repoUrl $localPath
          Write-Host "Successfully cloned GL Account Names dependency"
        } catch {
          Write-Host "Failed to clone dependency repository. Creating placeholder structure."
          New-Item -ItemType Directory -Path $localPath -Force
          # Create a placeholder app.json for dependency resolution
          $placeholderApp = @{
            id = "8ea23f33-25c7-4044-9ddf-60ae33c232fa"
            name = "G/L Acc. Names in Additional Languages by İnfotek"
            publisher = "Infotek Yazilim ve Donanim A.S."
            version = "*********"
          }
          $placeholderApp | ConvertTo-Json -Depth 3 | Out-File -FilePath "$localPath/app.json" -Encoding UTF8
        }

    - name: Create BC Container
      shell: powershell
      run: |
        $containerParams = @{
          accept_eula = $true
          containerName = "${{ env.CONTAINER_NAME }}"
          imageName = "mcr.microsoft.com/businesscentral/onprem:${{ env.BC_VERSION }}"
          auth = "UserPassword"
          username = "admin"
          password = "P@ssw0rd123!"
          updateHosts = $true
          useSSL = $false
          memoryLimit = "8G"
          isolation = "hyperv"
          includeTestToolkit = $true
          enableSymbolLoading = $true
          shortcuts = "None"
        }
        
        New-BcContainer @containerParams
        Wait-BcContainerReady -containerName "${{ env.CONTAINER_NAME }}" -timeout 600

    - name: Install dependency apps
      shell: powershell
      run: |
        # Install dependencies if app files are found
        $dependencyPaths = @(
          ".dependencies/QualityControlManagement",
          ".dependencies/GLAccountNames"
        )
        
        foreach ($depPath in $dependencyPaths) {
          if (Test-Path $depPath) {
            $appFiles = Get-ChildItem -Path $depPath -Filter "*.app" -Recurse
            foreach ($appFile in $appFiles) {
              try {
                Write-Host "Installing dependency: $($appFile.Name)"
                Publish-BcContainerApp -containerName "${{ env.CONTAINER_NAME }}" -appFile $appFile.FullName -sync -install
              } catch {
                Write-Host "Warning: Failed to install dependency $($appFile.Name): $($_.Exception.Message)"
              }
            }
          }
        }

    - name: Create output directory
      shell: powershell
      run: |
        New-Item -ItemType Directory -Path "output" -Force

    - name: Compile AL app
      shell: powershell
      run: |
        $appFileName = "${{ env.APP_NAME }}_${{ env.APP_VERSION }}.app".Replace(" ", "_")
        $appFilePath = "output/$appFileName"
        
        Compile-AppInBcContainer -containerName "${{ env.CONTAINER_NAME }}" -appProjectFolder "." -appOutputFile $appFilePath

    - name: Run AL tests
      shell: powershell
      continue-on-error: true
      run: |
        # Run tests if test codeunits exist
        try {
          Run-TestsInBcContainer -containerName "${{ env.CONTAINER_NAME }}" -testSuite "DEFAULT" -detailed
        } catch {
          Write-Host "Warning: Test execution failed or no tests found: $($_.Exception.Message)"
        }

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: maxwell-customizations-app
        path: output/*.app
        retention-days: 30

    - name: Cleanup container
      if: always()
      shell: powershell
      run: |
        Remove-BcContainer -containerName "${{ env.CONTAINER_NAME }}" -force

  update-dependencies:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Update dependency GitHub links in settings.json
      run: |
        # Read current settings.json
        if [ ! -f "settings.json" ]; then
          echo "settings.json not found, skipping dependency update"
          exit 0
        fi
        
        # Create updated settings.json with current repository URLs
        jq '.dependencies.appDependencies[0].githubRepo = "https://github.com/Maxwell-Food/Quality-Control-Management" |
            .dependencies.appDependencies[1].githubRepo = "https://github.com/Maxwell-Food/GL-Account-Names-Additional-Languages"' \
            settings.json > settings.json.tmp && mv settings.json.tmp settings.json

    - name: Commit updated settings.json
      run: |
        git config --global user.name 'GitHub Actions'
        git config --global user.email '<EMAIL>'
        
        if git diff --quiet settings.json; then
          echo "No changes to settings.json"
        else
          git add settings.json
          git commit -m "Update dependency GitHub links in settings.json [skip ci]"
          git push
        fi

  docker-publish:
    runs-on: windows-latest
    needs: build-and-test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup PowerShell modules
      shell: powershell
      run: |
        Set-PSRepository PSGallery -InstallationPolicy Trusted
        Install-Module BcContainerHelper -Force

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: maxwell-customizations-app
        path: output/

    - name: Setup Docker environment
      shell: powershell
      run: |
        # Enable Hyper-V if not already enabled (may require restart)
        Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All -NoRestart
        
        # Configure Docker for Windows containers
        & 'C:\Program Files\Docker\Docker\DockerCli.exe' -SwitchDaemon

    - name: Publish to Docker
      shell: powershell
      run: |
        .\PublishAppToDocker.ps1 -ContainerName "maxwell-bc-prod" -Force

    - name: Create deployment summary
      shell: powershell
      run: |
        $summary = @"
        ## 🚀 Deployment Summary
        
        **App**: ${{ env.APP_NAME }} v${{ env.APP_VERSION }}
        **Publisher**: ${{ env.PUBLISHER }}
        **Business Central Version**: ${{ env.BC_VERSION }}
        **Container**: maxwell-bc-prod
        **Deployment Time**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC')
        
        ### Dependencies Installed:
        - Quality Control Management (v********)
        - G/L Account Names in Additional Languages (v*********)
        
        ### Access Information:
        - Web Client: http://localhost:8080/BC/
        - Username: admin
        - Password: [Configured in settings.json]
        
        "@
        
        echo $summary >> $env:GITHUB_STEP_SUMMARY